import React, { useState } from "react";

// material-ui
import Button from "@mui/material/Button";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogTitle from "@mui/material/DialogTitle";
import { Table, TableBody, TableCell, TableHead, TableRow } from "@mui/material";

function SaveModal({ open, setOpen, editedRows, setSaving, updateLandDataBatch, getTableData }) {
    const [isSaved, setIsSaved] = useState(false);
    const [msg, setMsg] = useState("");

    const handleClose = (event, reason) => {
        if (reason && reason === "backdropClick")
            return;
        setOpen(false);
    };

    const handleSaveData = async () => {
        if (Object.keys(editedRows).length === 0) {
            return '沒有需要儲存的變更';
        }

        setSaving(true);
        try {
            const results = await updateLandDataBatch(editedRows);

            // 檢查結果
            const successCount = results.filter(r => r.success).length;
            const failCount = results.filter(r => !r.success).length;

            if (failCount === 0) {
                return `成功儲存 ${successCount} 筆變更！`;
            } else {
                const failedIds = results.filter(r => !r.success).map(r => r.id);
                return `儲存完成！成功: ${successCount} 筆，失敗: ${failCount} 筆\n失敗的項目: ${failedIds.join(', ')}`
            }


        } catch (error) {
            console.error("儲存過程發生錯誤:", error);
            return "儲存失敗，請稍後再試";
        } finally {
            setSaving(false);
        }
    };

    const handleSave = async () => {
        const tmpMsg = await handleSaveData();
        setMsg(tmpMsg);
        setIsSaved(true);
        setTimeout(() => {
            handleClose();
            setIsSaved(false);
            getTableData();
        }, 3000);
    };

    return (
        <div>
            {/* <Button variant="contained" onClick={handleClickOpen}>
                儲存
            </Button> */}
            <Dialog open={open} onClose={handleClose} maxWidth="lg" fullWidth>
                <DialogTitle>儲存</DialogTitle>
                <DialogContent>
                    {isSaved ? <p>{msg}</p> :
                        <Table>
                            <TableHead>
                                <TableRow sx={{ backgroundColor: "#f5f5f5" }}>
                                    <TableCell sx={{ border: "1px solid #ddd" }}>地段</TableCell>
                                    <TableCell sx={{ border: "1px solid #ddd" }}>地號</TableCell>
                                    <TableCell sx={{ border: "1px solid #ddd" }}>台灣座標系X座標 (TWD97)</TableCell>
                                    <TableCell sx={{ border: "1px solid #ddd" }}>台灣座標系Y座標 (TWD97)</TableCell>
                                    <TableCell sx={{ border: "1px solid #ddd" }}>全球座標系經度 (WGS84)</TableCell>
                                    <TableCell sx={{ border: "1px solid #ddd" }}>全球座標系緯度 (WGS84)</TableCell>
                                </TableRow>
                            </TableHead>
                            <TableBody>
                                {Object.values(editedRows).map((row) => (
                                    <TableRow key={`${row.landName}-${row.landSerialNumber}`}>
                                        <TableCell sx={{ border: "1px solid #ddd" }}>{row.landName}</TableCell>
                                        <TableCell sx={{ border: "1px solid #ddd" }}>{row.landSerialNumber}</TableCell>
                                        <TableCell sx={{ border: "1px solid #ddd" }}>{row.x}</TableCell>
                                        <TableCell sx={{ border: "1px solid #ddd" }}>{row.y}</TableCell>
                                        <TableCell sx={{ border: "1px solid #ddd" }}>{row.lat}</TableCell>
                                        <TableCell sx={{ border: "1px solid #ddd" }}>{row.long}</TableCell>
                                    </TableRow>
                                ))}
                            </TableBody>
                        </Table>}

                </DialogContent>
                {!isSaved &&
                    <DialogActions>
                        <Button onClick={handleClose}>取消</Button>
                        <Button onClick={handleSave}>儲存</Button>
                    </DialogActions>
                }
            </Dialog>
        </div>
    );
}

export default SaveModal;
