import axios from "axios";

// import en from "react-intl/src/en";

import queryString from "query-string";
import base64url from "base64url";

export const LOCALE_LANG = {
  LOCALE_DEFAULT: "zh-hans",
  LOCALE_ZH: "zh-hans",
  LOCALE_EN: "en",
};
let locale = LOCALE_LANG.LOCALE_DEFAULT;
export const dataSet = "south";

if (process.env.NODE_ENV === "production") {
  // baseUrl = "https://api.daoyidh.com/land";
  // Disable the console log in production.
  // eslint-disable-next-line no-console
  console.log = () => { };
} else {
  // baseUrl = process.env.REACT_APP_API_NODE;
  // baseUrl = "https://api2.daoyidh.com/land2";
}

const baseUrl = process.env.REACT_APP_API_NODE;

export const Api = {
  locale_lang: LOCALE_LANG,
  getLocale: () => locale,
  setLocale(lang) {
    locale = lang;
  },
  getAxios: () => axios,
  setAxiosAuth(token) {
    // 不加 prefix "Bearer "
    axios.defaults.headers.Authorization = `${token}`;
    axios.defaults.headers.common.Authorization = `${token}`;
  },
  // FIXME: maybe put the language json files into firestore.
  getLocaleJson: `${baseUrl}data`,

  //
  restfulCRUD: () => `${baseUrl}/{locale}/generic/2.0`,

  // 列出地段清單
  getLNList: () =>
    `${baseUrl}/{locale}/land/landname/1.0?limit=-1&offset=0&ds=${dataSet}`,

  // 列出地號清單
  getLSList: () =>
    `${baseUrl}/{locale}/land/landsn/1.0?limit=-1&offset=0&ds=${dataSet}&landName={landName}`,

  // 取得地段+地號的基本資料
  getBasicInfo: () =>
    `${baseUrl}/{locale}/land/data/info/1.0?limit=-1&offset=0&ds=${dataSet}&landName={landName}&landSerialNumber={landSerialNumber}`,

  // 有landID，取得基本資料
  getBasicInfoById: (landId) =>
    `${baseUrl}/{locale}/land/data/info/id/1.0?limit=-1&offset=0&ds=${dataSet}&landId=${landId}`,

  // 基本資料欄位排序
  getBasicInfoOrder: () =>
    `${baseUrl}/{locale}/land/land/order/1.1?limit=-1&offset=0`,

  // 有landId後，取得土地標示變更事件ID
  getLandMarkID: () =>
    `${baseUrl}/{locale}/land/data/landmark/2.0?limit=-1&offset=0&ds=${dataSet}&landId={landId}`,

  // landMark欄位順序
  getLandMarkOrder: () =>
    `${baseUrl}/{locale}/land/landmark/order/1.0?limit=-1&offset=0`,

  // 取得每個Lank Mark events
  getLandMarkEvents: () =>
    `${baseUrl}/{locale}/land/landmark/data/1.0?limit=-1&offset=0&ds=${dataSet}&ids={ids}`,

  // landRights欄位順序
  getLandRightsOrder: () =>
    `${baseUrl}/{locale}/land/landrights/order/1.0?limit=-1&offset=0`,

  // 有landID後，取得土地權利變更事件ID
  getLandRightsID: () =>
    `${baseUrl}/{locale}/land/data/landrights/2.0?limit=-1&offset=0&ds=${dataSet}&landId={landId}`,

  // 取得每個Lank Right events
  getLandRightsEvents: () =>
    `${baseUrl}/{locale}/land/landrights/data/1.0?limit=-1&offset=0&ds=${dataSet}&ids={ids}`,

  // 搜尋欄位清單
  getSearchColNames: () =>
    `${baseUrl}/{locale}/land/fields/list/1.0?limit=-1&offset=0`,

  // 根據欄位搜尋關鍵字，得到 LandId
  getSearchInfo: () =>
    `${baseUrl}/{locale}/land/search/fields/1.0?limit=-1&offset=0&ds=${dataSet}&ids={ids}&keyword={keyword}`,

  // 根據欄位搜尋關鍵字，得到 LandId
  getPostSearchInfo: () =>
    `${baseUrl}/{locale}/post/land/search/fields/1.0?limit=-1&offset=0&ds=${dataSet}&ids=&keyword=`,

  // 取得[地段+地號]的歷史紀錄eventId
  getHistoryId: () =>
    `${baseUrl}/{locale}/land/data/historyevent/1.0?limit=-1&offset=0&ds=${dataSet}&landName={landName}&landSerialNumber={landSerialNumber}`,

  // 依據history event Id取得所有資料
  getHistoryEvents: () =>
    `${baseUrl}/{locale}/land/historyevent/data/1.0?limit=-1&offset=0&ds=${dataSet}&ids={ids}`,

  // 使用者編輯中的土地資料
  getEditedLandByUser: (lockUser) =>
    `${baseUrl}/{locale}/land/lockUser/1.0?limit=-1&offset=0&ds=${dataSet}&lockUser=${lockUser}`,

  // 根據landID取得土地資料
  getLandData: () =>
    `${baseUrl}/{locale}/land/data/info/search/1.0?limit=-1&offset=0&ds=${dataSet}&ids={ids}`,

  // 根據landID取得土地資料
  getPostLandData: () =>
    `${baseUrl}/{locale}/post/land/data/info/search/1.0?limit=-1&offset=0&ds=${dataSet}&ids=`,

  // EditPage，下拉選單欄位選項
  getColDropDownOptions: () =>
    `${baseUrl}/{locale}/land/property/list/1.0?limit=-1&offset=0&ds=${dataSet}&class={class}&property={property}`,

  // EditPage，取得業主清單
  getOwnerList: () =>
    `${baseUrl}/{locale}/land/value/list/1.0?limit=-1&offset=0&ds=${dataSet}&class=Owner`,

  // DownloadPage，landMark search type1
  getLMType1: () =>
    `${baseUrl}/{locale}/land/download/landmark/type1/1.0?limit=-1&offset=0&landName={landName}`,

  // DownloadPage，landMark search type2
  getLMType2: () =>
    `${baseUrl}/{locale}/land/download/landmark/type2/1.0?limit=-1&offset=0&landName={landName}&landSerialNumber={landSerialNumber}`,

  // DownloadPage，landRights search type1
  getLRType1: () =>
    `${baseUrl}/{locale}/land/download/landrights/type1/1.0?limit=-1&offset=0&landName={landName}`,

  // DownloadPage，landRights search type2
  getLRType2: () =>
    `${baseUrl}/{locale}/land/download/landrights/type2/1.0?limit=-1&offset=0&landName={landName}&landSerialNumber={landSerialNumber}`,

  // DownloadPage，根據選擇日期得到historyID、landID
  getHisIDByDate: () =>
    `${baseUrl}/{locale}/land/download/landeditor/1.1?limit=-1&offset=0&startDate={startDate}&endDate={endDate}`,

  // 取得目前Event的ID max number
  getEventIDMaxNum: () =>
    `${baseUrl}/{locale}/land/download/maxInstanceNumber/1.0?limit=1&offset=0&type={type}&typePrefix={typePrefix}`,

  // 取得所有Operator名單
  getLandOperator: () =>
    `${baseUrl}/{locale}/land/data/operator/1.0?limit=-1&offset=0`,

  // 取得土地權利or標示變更年分清單
  getLandYearList: () =>
    `${baseUrl}/{locale}/land/startDate/yearList/1.0?limit=-1&offset=0&landName={landName}&type={type}`,

  // 土地變更事件清單
  getLandEvtTypes: () =>
    `${baseUrl}/{locale}/land/eventTypes/1.0?limit=-1&offset=0`,

  // import data
  importToDB: `${baseUrl}/import/file/{graph}?type=landImport`,

  // HistoryPage:
  // 取得歷史紀錄時間列表
  getHistoryDateList: () =>
    `${baseUrl}/{locale}/land/historyevent/dateList/1.0?limit=-1&offset=0`,

  // 取得歷史紀錄時間列表單日資訊
  getHistoryDateListInfo: () =>
    `${baseUrl}/{locale}/land/historyevent/dateList/info/1.0?limit=-1&offset=0&dateStr={dateStr}`,

  // 取得歷史紀錄編輯者列表
  getHistoryUserList: () =>
    `${baseUrl}/{locale}/land/historyevent/userList/1.0?limit=-1&offset=0`,

  // 取得歷史紀錄編輯者列表單人編輯資訊
  getHistoryUserListInfo: () =>
    `${baseUrl}/{locale}/land/historyevent/userList/info/1.0?limit=-1&offset=0&userName={userName}`,

  // 每筆土地各地號的土地權利、標示事件變更次數
  getStsType1: () =>
    `${baseUrl}/{locale}/land/statistics/type1/1.0?limit=-1&offset=0&landName={landName}&landSN={landSN}`,

  // 經緯度頁面表格資料
  getGisTableData: () =>
    `${baseUrl}/{locale}/gis/table/data/1.0?limit=-1&offset=0`,
};

const excEncodeQueryParam = ["limit", "offset"];

const encodeQueryStr = (queryStr) => {
  const queryObj = queryString.parse(queryStr);
  const encodeQueryStr = [];
  Object.keys(queryObj).forEach((qo) => {
    let val = queryObj[qo];
    if (excEncodeQueryParam.indexOf(qo) < 0) {
      val = base64url.encode(val);
      queryObj[qo] = val;
    }
    encodeQueryStr.push(`${qo}=${val}`);
  });
  return encodeQueryStr.join("&");
};

export const encodeUrl = (apiStr) => {
  const preApiStr = apiStr.split("?")[0];
  const queryStr = apiStr.split("?")[1];

  if (!queryStr) return apiStr;

  const queryStrEncode = encodeQueryStr(queryStr);

  return `${preApiStr}?${queryStrEncode}`;
};

export const addAxiosInterceptor = (_axios, _millisToSS, _millisToMMSS) => {
  const addDurationAttr = (obj, duration) => {
    if (_millisToSS) obj.durationSS = _millisToSS(duration);
    if (_millisToMMSS) obj.durationMMSS = _millisToMMSS(duration);
    return obj;
  };

  // Add a request interceptor
  _axios.interceptors.request.use(
    (config) => {
      // Do something before request is sent
      config.metadata = { startTime: new Date() };
      return config;
    },
    (error) =>
      // Do something with request error
      Promise.reject(error)
  );

  // Add a response interceptor
  _axios.interceptors.response.use(
    (response) => {
      // Any status code that lie within the range of 2xx cause this function to trigger
      // Do something with response data
      response.config.metadata.endTime = new Date();
      response.duration =
        response.config.metadata.endTime - response.config.metadata.startTime;
      return addDurationAttr(response, response.duration);
    },
    (error) => {
      // Any status codes that falls outside the range of 2xx cause this function to trigger
      // Do something with response error
      error.config.metadata.endTime = new Date();
      error.duration =
        error.config.metadata.endTime - error.config.metadata.startTime;
      return addDurationAttr(error, error.duration);
    }
  );
};

export const readOntoData = (
  apiStr,
  timeout = 15000,
  debug = false,
  isCancelled = false,
  locale
) => {
  // lang
  const curLocale = locale || Api.getLocale();
  let api = apiStr.replace("{locale}", curLocale);

  // timeout
  axios.defaults.timeout = timeout;
  // console.log("readHkbdbData timeout", timeout);
  // encode query string (except limit and offset)
  // api = encodeUrl(api);

  try {
    // abort if it tyr too long
    const controller = new AbortController();
    // const signal = controller.signal;
    // to stop fetch if timeout
    if (isCancelled) {
      controller.abort();
    } else {
      setTimeout(() => controller.abort(), timeout);
    }
    // replace api prefix for docker in production
    if (process.env.NODE_ENV === "production") {
      if (process.env.REACT_APP_MODE === "docker") {
        api = api.replace(process.env.REACT_APP_API_NODE, "");
      }
    }

    // debug
    if (debug) {
      console.log(api);
      console.log("Authorization axios", axios.defaults.headers);
    }

    // fetch nmtl api
    return (
      axios
        .get(api)
        // handle data to json
        .then((response) => ({
          data: response?.data?.data || [],
          durationSS: response?.durationSS,
        }))
        // handle catch
        .catch((err) => {
          // handle AbortController event and return error message
          if (err.name === "AbortError") {
            console.error("api:hkbdb:fetch:get:aborted:cancle:or:timeout");
            return { data: [], error: err.message };
          }
          // handle other error event and return error message

          console.error("api:hkbdb:fetch:get:error:", err.message);
          return { data: [], error: err.message };
        })
    );
  } catch (err) {
    console.error("api:hkbdb:fetch:catch:error: ", err.message);
    return { data: [], error: err.message };
  }
};

export const fetchPOSTData = async ({ apiStr, entry }) => {
  try {
    // lang
    const curLocale = locale || Api.getLocale();
    const api = apiStr.replace("{locale}", curLocale);
    const response = await axios.post(api, { entry });
    return {
      state: response?.data?.data === "OK",
      data: response?.data?.data || [],
    };
  } catch (err) {
    console.error("api:fetchPOSTData:catch:error: ", err.message);
    return { state: false, error: err.message };
  }
};

/**
 * for queryPage
 * @param apiStr
 * @param queryStr
 * @param limit
 * @param offset
 * @param timeout
 * @returns {Promise<AxiosResponse<T> | {head: {}, total: number, error: *, results: {}}>|{head: {}, total: number, error: *, results: {}}}
 */
export const queryOntoData = (
  apiStr,
  queryStr,
  limit,
  offset,
  timeout = 5000
) => {
  // timeout
  axios.defaults.timeout = timeout;
  // console.log("I am queryHKbdbData", apiStr);
  try {
    // post data
    const data = {
      query: queryStr,
      limit,
      offset,
    };
    // axios nmtl api
    return axios
      .post(apiStr, data)
      .then((res) => {
        if (res && res.data) res.data.durationSS = res?.durationSS;
        return res.data;
      })
      .catch((err) => {
        console.error("api:axios:catch:error: ", err.message);
        return { head: {}, results: {}, total: -1, error: err.message };
      });
  } catch (err) {
    console.error("api:axios:catch:error: ", err.message);
    return { head: {}, results: {}, total: -1, error: err.message };
  }
};

// delete
export const deleteOntoData = (
  apiStr,
  entry,
  limit,
  offset,
  timeout = 5000
) => {
  const api = apiStr.replace("{locale}", Api.getLocale());
  console.log("I am deleteHkbdbData", api);
  // timeout
  axios.defaults.timeout = timeout;
  // console.log("I am queryHKbdbData", apiStr);
  const config = {
    // headers: {
    //     Authorization: ""
    // },
    data: { entry },
  };
  //
  try {
    // axios api
    return axios
      .delete(api, config)
      .then((res) => ({ state: res?.data?.data === "OK" }))
      .catch((err) => {
        console.error("api:axios:catch:error: ", err.message);
        return { state: false, error: err.message };
      });
  } catch (err) {
    console.error("api:axios:catch:error: ", err.message);
    return { state: false, error: err.message };
  }
};

// update
export const updateOntoData = (
  apiStr,
  entrySrc,
  entryDst,
  limit,
  offset,
  timeout = 5000
) => {
  const api = apiStr.replace("{locale}", Api.getLocale());
  console.log("I am updateHkbdbData", api);
  // timeout
  axios.defaults.timeout = timeout;
  // console.log("I am queryHKbdbData", apiStr);
  const entry = {
    entrySrc,
    entryDst,
  };
  //
  try {
    // axios api
    return axios
      .put(api, entry)
      .then((res) => ({ state: res?.data?.data === "OK" }))
      .catch((err) => {
        console.error("api:axios:catch:error: ", err.message);
        return { state: false, error: err.message };
      });
  } catch (err) {
    console.error("api:axios:catch:error: ", err.message);
    return { state: false, error: err.message };
  }
};

// create
export const createOntoData = (apiStr, entry, timeout = 5000) => {
  const api = apiStr.replace("{locale}", Api.getLocale());
  console.log("I am createHkbdbData", api);
  // timeout
  axios.defaults.timeout = timeout;
  // console.log("I am queryHKbdbData", apiStr);
  try {
    // axios api
    return (
      axios
        .post(api, { entry })
        // .then(res => ({ state: res.data === "OK" }))
        .then((res) => ({ state: res?.data?.data === "OK" }))
        .catch((err) => {
          console.error("api:axios:catch:error: ", err.message);
          return { state: false, error: err.message };
        })
    );
  } catch (err) {
    console.error("api:axios:try:catch:error: ", err.message);
    return { state: false, error: err.message };
  }
};
