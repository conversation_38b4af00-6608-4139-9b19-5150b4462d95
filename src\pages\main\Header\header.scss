.Header {
  height: 10%;
  margin: 6px 8px;
  padding: 6px 8px;
  display: flex;
  align-items: center;
  border: 1px gray solid;
  justify-content: space-between;
  .Box {
    display: flex;
    align-items: center;
    text-align: center;
    height: 80%;
    .HeaderItem {
      min-width: 100px;
      //background-color: lightgray;
      height: 100%;
      button {
        width: 100%;
      }
    }
    .hrLine {
      height: 5px;
      width: 100%;
      background-color: black;
      border-radius: 15%;
    }
  }
}