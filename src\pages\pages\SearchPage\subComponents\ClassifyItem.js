import React, { useContext, useState, useEffect } from "react";

// material-ui
import {
  Button,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Checkbox,
} from "@mui/material";
import ArrowDropDownIcon from "@mui/icons-material/ArrowDropDown";
import AddIcon from "@mui/icons-material/Add";
import RemoveIcon from "@mui/icons-material/Remove";

//
import { StoreContext } from "../../../../store/StoreProvider";
import Act from "../../../../store/actions";

function ClassifyItem() {
  const [anchorEl, setAnchorEl] = useState(null);
  const open = Boolean(anchorEl);
  const [state, dispatch] = useContext(StoreContext);
  const { searchColList } = state.search;
  const [checkAll, setCheckAll] = useState(true);

  useEffect(() => {
    let tmpCheckAll = true;
    searchColList.forEach((element) => {
      if (!element.select) {
        tmpCheckAll = false;
      }
    });
    setCheckAll(tmpCheckAll);
  }, [searchColList]);

  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleSelectAll = () => {
    setCheckAll(!checkAll);
    let tmpSearchColList = JSON.parse(JSON.stringify(searchColList));
    tmpSearchColList = tmpSearchColList.map((element) => ({
      ...element,
      select: !checkAll,
    }));
    dispatch({
      type: Act.SET_SEARCHCOLLIST,
      payload: tmpSearchColList,
    });
  };

  const handleEachSelect = (item) => {
    const tmpSearchColList = JSON.parse(JSON.stringify(searchColList));
    const findItem = tmpSearchColList.find(
      (element) => element.label === item.label
    );
    if (findItem) {
      findItem.select = !findItem.select;
    }
    dispatch({
      type: Act.SET_SEARCHCOLLIST,
      payload: tmpSearchColList,
    });
  };

  return (
    <div className="ClassifyItem">
      <Button
        variant="contained"
        onClick={handleClick}
        endIcon={<ArrowDropDownIcon />}
      >
        請選擇搜尋類別
      </Button>
      <Menu open={open} onClose={handleClose} anchorEl={anchorEl}>
        <MenuItem>
          <Checkbox checked={checkAll} onChange={handleSelectAll} />
          <ListItemText>選擇全部</ListItemText>
        </MenuItem>
        {searchColList.map((item, index) => (
          <MenuItem key={index} onClick={() => handleEachSelect(item)}>
            <ListItemIcon>
              {item.select ? <AddIcon /> : <RemoveIcon />}
            </ListItemIcon>
            <ListItemText>{item.label}</ListItemText>
          </MenuItem>
        ))}
      </Menu>
    </div>
  );
}

export default ClassifyItem;
