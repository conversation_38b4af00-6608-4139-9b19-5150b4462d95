<?xml version="1.0" encoding="UTF-8"?>
<WebElementEntity>
   <description></description>
   <name>button_Sign in with emailEmail</name>
   <tag></tag>
   <elementGuidId>035e6a65-8913-4d59-abe3-a7a62f5545ab</elementGuidId>
   <selectorCollection>
      <entry>
         <key>CSS</key>
         <value>button.firebaseui-idp-button.mdl-button.mdl-js-button.mdl-button--raised.firebaseui-idp-password.firebaseui-id-idp-button</value>
      </entry>
      <entry>
         <key>XPATH</key>
         <value>//div[@id='firebaseui_container']/div/div/form/ul/li[2]/button</value>
      </entry>
   </selectorCollection>
   <selectorMethod>XPATH</selectorMethod>
   <smartLocatorCollection>
      <entry>
         <key>SMART_LOCATOR</key>
         <value>internal:role=button[name=&quot;Sign in with email&quot;i]</value>
      </entry>
   </smartLocatorCollection>
   <smartLocatorEnabled>false</smartLocatorEnabled>
   <useRalativeImagePath>true</useRalativeImagePath>
   <webElementProperties>
      <isSelected>false</isSelected>
      <matchCondition>equals</matchCondition>
      <name>tag</name>
      <type>Main</type>
      <value>button</value>
      <webElementGuid>550796b1-85dc-49da-ae59-144eedc27c06</webElementGuid>
   </webElementProperties>
   <webElementProperties>
      <isSelected>false</isSelected>
      <matchCondition>equals</matchCondition>
      <name>class</name>
      <type>Main</type>
      <value>firebaseui-idp-button mdl-button mdl-js-button mdl-button--raised firebaseui-idp-password firebaseui-id-idp-button</value>
      <webElementGuid>5c12a563-9b28-4d44-93f0-4a00349d30d6</webElementGuid>
   </webElementProperties>
   <webElementProperties>
      <isSelected>false</isSelected>
      <matchCondition>equals</matchCondition>
      <name>data-provider-id</name>
      <type>Main</type>
      <value>password</value>
      <webElementGuid>8abe2661-9caa-40c9-b2bb-e160a37d85c5</webElementGuid>
   </webElementProperties>
   <webElementProperties>
      <isSelected>false</isSelected>
      <matchCondition>equals</matchCondition>
      <name>data-upgraded</name>
      <type>Main</type>
      <value>,MaterialButton</value>
      <webElementGuid>609b9756-8890-4d53-a6fc-64c581540ccb</webElementGuid>
   </webElementProperties>
   <webElementProperties>
      <isSelected>true</isSelected>
      <matchCondition>equals</matchCondition>
      <name>text</name>
      <type>Main</type>
      <value>Sign in with emailEmail</value>
      <webElementGuid>e89bbf5f-5426-44cd-93fd-33a9fe0ad99a</webElementGuid>
   </webElementProperties>
   <webElementProperties>
      <isSelected>false</isSelected>
      <matchCondition>equals</matchCondition>
      <name>xpath</name>
      <type>Main</type>
      <value>id(&quot;firebaseui_container&quot;)/div[@class=&quot;firebaseui-container firebaseui-page-provider-sign-in firebaseui-id-page-provider-sign-in firebaseui-use-spinner&quot;]/div[@class=&quot;firebaseui-card-content&quot;]/form[1]/ul[@class=&quot;firebaseui-idp-list&quot;]/li[@class=&quot;firebaseui-list-item&quot;]/button[@class=&quot;firebaseui-idp-button mdl-button mdl-js-button mdl-button--raised firebaseui-idp-password firebaseui-id-idp-button&quot;]</value>
      <webElementGuid>1c37559a-007c-40de-92e9-39c459c07b62</webElementGuid>
   </webElementProperties>
   <webElementXpaths>
      <isSelected>true</isSelected>
      <matchCondition>equals</matchCondition>
      <name>xpath:idRelative</name>
      <type>Main</type>
      <value>//div[@id='firebaseui_container']/div/div/form/ul/li[2]/button</value>
      <webElementGuid>5cc201f3-355f-4701-a4ed-4cae7fa6e5df</webElementGuid>
   </webElementXpaths>
   <webElementXpaths>
      <isSelected>false</isSelected>
      <matchCondition>equals</matchCondition>
      <name>xpath:neighbor</name>
      <type>Main</type>
      <value>(.//*[normalize-space(text()) and normalize-space(.)='Google'])[1]/following::button[1]</value>
      <webElementGuid>bd0b7911-9d31-4c59-903b-15a4e008fee4</webElementGuid>
   </webElementXpaths>
   <webElementXpaths>
      <isSelected>false</isSelected>
      <matchCondition>equals</matchCondition>
      <name>xpath:neighbor</name>
      <type>Main</type>
      <value>(.//*[normalize-space(text()) and normalize-space(.)='Sign in with Google'])[1]/following::button[1]</value>
      <webElementGuid>2daf4760-df25-48e4-883a-0ff1757ff295</webElementGuid>
   </webElementXpaths>
   <webElementXpaths>
      <isSelected>false</isSelected>
      <matchCondition>equals</matchCondition>
      <name>xpath:position</name>
      <type>Main</type>
      <value>//li[2]/button</value>
      <webElementGuid>91a74f75-4eff-489b-b193-14ca5d49c383</webElementGuid>
   </webElementXpaths>
   <webElementXpaths>
      <isSelected>false</isSelected>
      <matchCondition>equals</matchCondition>
      <name>xpath:customAttributes</name>
      <type>Main</type>
      <value>//button[(text() = 'Sign in with emailEmail' or . = 'Sign in with emailEmail')]</value>
      <webElementGuid>aa69d81b-40b7-4264-8d91-e4dd9c400c46</webElementGuid>
   </webElementXpaths>
</WebElementEntity>
