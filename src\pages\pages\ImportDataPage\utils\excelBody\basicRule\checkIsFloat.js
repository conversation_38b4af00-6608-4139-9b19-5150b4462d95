import { specRow } from "../../../config";

/**
 * 檢查說明: 只能填小數
 * */
const checkIsFloat = (cell, worksheet) => {
  const colLabel = worksheet
    .getRow(specRow.labelIdx)
    .getCell(cell.fullAddress.col).value;

  let tmpResStr = "";

  const safeVal =
    typeof cell.value === "string" ? parseFloat(cell.value) : cell.value;

  if (
    cell.value &&
    typeof safeVal === "number" &&
    !Number.isNaN(safeVal) &&
    Number.isInteger(safeVal)
  ) {
    const reason = "只能填小數";
    tmpResStr += `${cell.address}, [${cell.value}], 欄位:${colLabel}，${reason}。\n`;
  }

  return tmpResStr;
};

export default checkIsFloat;
