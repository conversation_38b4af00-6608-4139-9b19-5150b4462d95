const Act = {
  // test
  TEST_INFO: "TEST_INFO",
  // firebase
  FIREBASE_LOGIN_USER: "FIREBASE_LOGIN_USER",
  FIREBASE_LOGOUT_USER: "FIREBASE_LOGOUT_USER",

  // editorPage
  EDIT_SET_INIT: "EDIT_SET_INIT",
  SET_ISLOCK: "SET_ISLOCK",
  SET_LANDID: "SET_LANDID",
  SET_LANDNAME: "SET_LANDNAME",
  SET_LANDSERIALNUMBER: "SET_LANDSERIALNUMBER",
  SET_EXTRABASIC: "SET_EXTRABASIC",
  SET_BASICINFO: "SET_BASICINFO",
  SET_LANDMARKINFO: "SET_LANDMARKINFO",
  SET_LANDRIGHTINFO: "SET_LANDRIGHTINFO",
  SET_SELECTIONINDEX: "SET_SELECTIONINDEX",
  SET_LSNLIST: "SET_LSNLIST",
  SET_EDITLOADING: "SET_EDITLOADING",
  SET_LOCKERUSER: "SET_LOCKERUSER",
  SET_INFOLOADING: "SET_INFOLOADING",
  SET_SHOWSAVEBTNPOPPER: "SET_SHOWSAVEBTNPOPPER",
  SET_ADDSTRDIALOGPARAM: "SET_ADDSTRDIALOGPARAM",
  SET_STARTYEAR: "SET_STARTYEAR",
  SET_ENDYEAR: "SET_ENDYEAR",

  // searchPage
  SEARCH_SET_INIT: "SEARCH_SET_INIT",
  SET_KEYWORD: "SET_KEYWORD",
  SET_SEARCHCOLLIST: "SET_SEARCHCOLLIST",
  SET_RESULTLIST: "SET_RESULTLIST",
  SET_SEARCHLOADING: "SET_SEARCHLOADING",
  SET_PAGENUMBER: "SET_PAGENUMBER",
  SET_SEARCHDURATION: "SET_SEARCHDURATION",

  // downloadPage
  SET_LANDTYPE: "SET_LANDTYPE",
  SET_DATAFORMTYPE: "SET_DATAFORMTYPE",
  SET_DWLANDNAME: "SET_DWLANDNAME",
  SET_DWLANDSERIALNUMBER: "SET_DWLANDSERIALNUMBER",
  SET_SELECTDATETYPE: "SET_SELECTDATETYPE",

  // import page
  SET_FILEDATA: "SET_FILEDATA",

  // authorityPage
  SET_AUTPAGELOADING: "SET_AUTPAGELOADING",
  SET_USERINFO: "SET_USERINFO",

  // main
  SET_WEB_STYLE: "SET_WEB_STYLE",
  SET_DEFAULT_WEB_STYLE: "SET_DEFAULT_WEB_STYLE",
  SET_IMAGE: "SET_IMAGE",
  SET_IMAGE_TOKEN: "SET_IMAGE_TOKEN",
  SET_IMAGE_DESK_BG: "SET_IMAGE_DESK_BG",
  SET_IMAGE_DESK_BANNER: "SET_IMAGE_DESK_BANNER",
  SET_IMAGE_DESK_LOGO: "SET_IMAGE_DESK_LOGO",
  SET_IMAGE_MOBILE_BG: "SET_IMAGE_MOBILE_BG",
  SET_IMAGE_MOBILE_BANNER: "SET_IMAGE_MOBILE_BANNER",
  SET_IMAGE_MOBILE_LOGO: "SET_IMAGE_MOBILE_LOGO",
  SET_SWG_JSON: "SET_SWG_JSON",
  SET_SWG_JSON_FILE: "SET_SWG_JSON_FILE",
  SET_PRODUCTION: "SET_PRODUCTION",
  REFRESH_ANONYMOUS_TOKEN: "REFRESH_ANONYMOUS_TOKEN",
  SET_DATABASE: "SET_DATABASE",
  SET_LOADING: "SET_LOADING",
};

export default Act;
