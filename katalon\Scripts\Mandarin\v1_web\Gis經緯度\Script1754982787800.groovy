import static com.kms.katalon.core.checkpoint.CheckpointFactory.findCheckpoint
import static com.kms.katalon.core.testcase.TestCaseFactory.findTestCase
import static com.kms.katalon.core.testdata.TestDataFactory.findTestData
import static com.kms.katalon.core.testobject.ObjectRepository.findTestObject
import static com.kms.katalon.core.testobject.ObjectRepository.findWindowsObject
import com.kms.katalon.core.checkpoint.Checkpoint as Checkpoint
import com.kms.katalon.core.cucumber.keyword.CucumberBuiltinKeywords as CucumberKW
import com.kms.katalon.core.mobile.keyword.MobileBuiltInKeywords as Mobile
import com.kms.katalon.core.model.FailureHandling as FailureHandling
import com.kms.katalon.core.testcase.TestCase as TestCase
import com.kms.katalon.core.testdata.TestData as TestData
import com.kms.katalon.core.testng.keyword.TestNGBuiltinKeywords as TestNGKW
import com.kms.katalon.core.testobject.TestObject as TestObject
import com.kms.katalon.core.testobject.ConditionType as ConditionType
import com.kms.katalon.core.webservice.keyword.WSBuiltInKeywords as WS
import com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords as WebUI
import com.kms.katalon.core.windows.keyword.WindowsBuiltinKeywords as Windows
import internal.GlobalVariable as GlobalVariable
import org.openqa.selenium.Keys as Keys

WebUI.openBrowser('')

WebUI.navigateToUrl('http://localhost:3000/Gis')

WebUI.click(findTestObject('Object Repository/Mandarin/v1_web/Page_/button_'))

WebUI.click(findTestObject('Object Repository/Mandarin/v1_web/Page_/button_Sign in with emailEmail'))

WebUI.setText(findTestObject('Object Repository/Mandarin/v1_web/Page_/input_Email_email'), '<EMAIL>')

WebUI.click(findTestObject('Object Repository/Mandarin/v1_web/Page_/button_Next'))

WebUI.setEncryptedText(findTestObject('Object Repository/Mandarin/v1_web/Page_/input_Password_password'), '0D41p6Ct1VJ35FCiyaw3Gw==')

WebUI.click(findTestObject('Object Repository/Mandarin/v1_web/Page_/button_Sign In'))

WebUI.click(findTestObject('Object Repository/Mandarin/v1_web/Page_/button__1'))

WebUI.setText(findTestObject('Object Repository/Mandarin/v1_web/Page_/input__mui-3'), '新北勢')

// === 改這段 ===
// 點擊下拉按鈕（role="combobox"）
TestObject dropdownButton = new TestObject()
dropdownButton.addProperty("css", ConditionType.EQUALS, 'div[role="combobox"]')
WebUI.click(dropdownButton)

// 等待選單出現並選擇「每頁顯示 50 筆」
TestObject option50 = new TestObject()
option50.addProperty("css", ConditionType.EQUALS, 'li.MuiMenuItem-root[data-value="50"]')
WebUI.waitForElementVisible(option50, 5)
WebUI.click(option50)
// === 改到這裡 ===

WebUI.setText(findTestObject('Object Repository/Mandarin/v1_web/Page_/input__mui-65'), '1')
WebUI.setText(findTestObject('Object Repository/Mandarin/v1_web/Page_/input__mui-66'), '2')
WebUI.click(findTestObject('Object Repository/Mandarin/v1_web/Page_/pagination'))

WebUI.setText(findTestObject('Object Repository/Mandarin/v1_web/Page_/input__mui-165'), '3')
WebUI.setText(findTestObject('Object Repository/Mandarin/v1_web/Page_/input__mui-166'), '4')
WebUI.click(findTestObject('Object Repository/Mandarin/v1_web/Page_/pagination_1'))

WebUI.click(findTestObject('Object Repository/Mandarin/v1_web/Page_/button_save'))
WebUI.waitForAlert(5)
WebUI.acceptAlert()

// 驗證輸入值是否正確
WebUI.verifyEqual(WebUI.getAttribute(findTestObject('Object Repository/Mandarin/v1_web/Page_/input__mui-125'), 'value'), '1')
WebUI.verifyEqual(WebUI.getAttribute(findTestObject('Object Repository/Mandarin/v1_web/Page_/input__mui-126'), 'value'), '2')
WebUI.click(findTestObject('Object Repository/Mandarin/v1_web/Page_/pagination'))

WebUI.verifyEqual(WebUI.getAttribute(findTestObject('Object Repository/Mandarin/v1_web/Page_/input__mui-225'), 'value'), '3')
WebUI.verifyEqual(WebUI.getAttribute(findTestObject('Object Repository/Mandarin/v1_web/Page_/input__mui-226'), 'value'), '4')

//


//WebUI.closeBrowser()
