@use "../../../scss/common";

.searchArea {
  //height: 20%;
  //border: 1px blue solid;
  .inputCol {
    margin-bottom: 5px;
    .SearchInput {
      display: flex;
      align-items: flex-end;
    }
    .ClassifyItem{
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
      button {
        font-size: 1.2rem;
      }
    }

    .ClearAll {
      display: flex;
      align-items: center;
      height: 100%;
      button {
        font-size: 1.2rem;
      }
    }
  }
  .labelCol {
    margin-bottom: 5px;
    //border: 1px red solid;
    padding: 0 0 5px 0;
  }
}
.resultField {
  height: 60vh;
  border-radius: 8px;
  box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
  overflow-y: scroll;
  padding: 1% 0;
  //border: 1px red solid;
}
