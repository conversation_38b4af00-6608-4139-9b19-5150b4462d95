import React, { useEffect, useState } from "react";

// material ui
import List from "@mui/material/List";
import ListItem from "@mui/material/ListItem";
import ListItemButton from "@mui/material/ListItemButton";
import ListItemIcon from "@mui/material/ListItemIcon";
import ListItemText from "@mui/material/ListItemText";
import Collapse from "@mui/material/Collapse";
import DateRangeIcon from "@mui/icons-material/DateRange";
import ExpandLess from "@mui/icons-material/ExpandLess";
import ExpandMore from "@mui/icons-material/ExpandMore";
import WorkHistoryIcon from "@mui/icons-material/WorkHistory";

// api
import { Api, readOntoData } from "../../../../api/land/Api";

function DateClassify() {
  const [sortedList, setSortedList] = useState([]);

  useEffect(() => {
    const apiStr = Api.getHistoryDateList();
    readOntoData(apiStr).then((res) => {
      const sortByDate = res.data
        .sort(
          (cur, next) => Date.parse(next.YYYYMMDD) - Date.parse(cur.YYYYMMDD)
        )
        .map((el) => ({ ...el, open: false, infoData: [] }));
      setSortedList(sortByDate);
    });
  }, []);

  const handleClick = async (evt) => {
    const tmpList = JSON.parse(JSON.stringify(sortedList));
    const findObj = tmpList.find((el) => el.YYYYMMDD === evt.target.innerText);
    if (findObj) {
      findObj.open = !findObj.open;

      if (findObj.open) {
        const apiStr = Api.getHistoryDateListInfo().replace(
          "{dateStr}",
          findObj.YYYYMMDD
        );
        const tmpRes = await readOntoData(apiStr);
        findObj.infoData = tmpRes.data;
      } else {
        findObj.infoData = [];
      }
      setSortedList(tmpList);
    }
  };

  return (
    <List component="nav">
      {sortedList.map((el) => {
        const { YYYYMMDD, open, infoData } = el;
        return (
          <React.Fragment key={el.YYYYMMDD}>
            <ListItemButton onClick={handleClick}>
              <ListItemIcon>
                <DateRangeIcon />
              </ListItemIcon>
              <ListItemText primary={YYYYMMDD} />
              {open ? <ExpandLess /> : <ExpandMore />}
            </ListItemButton>
            <Collapse in={open}>
              {infoData.map((info) => (
                <List component="div" key={info.hisId}>
                  <ListItem
                    sx={{ pl: 4 }}
                    style={{
                      display: "grid",
                      gridTemplateColumns: "50px 10% 1fr",
                    }}
                  >
                    <ListItemIcon>
                      <WorkHistoryIcon />
                    </ListItemIcon>
                    <ListItemText primary={info.user} />
                    <ListItemText primary={info.action} />
                  </ListItem>
                </List>
              ))}
            </Collapse>
          </React.Fragment>
        );
      })}
    </List>
  );
}

export default DateClassify;
