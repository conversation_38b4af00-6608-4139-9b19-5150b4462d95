import checkLandBasic from "../basicRule/checkLandBasic";
import checkMustHas from "../basicRule/checkMustHas";
import getLMLRNumber from "../basicRule/getLMLRNumber";

/**
 * 檢查說明: check landName and landSerialNumber information
 * */
const checkLandNSN = (cell, worksheet) => {
  let tmpStr = "";

  tmpStr = checkLandBasic(cell, worksheet);
  if (!tmpStr) {
    const specialColVal = getLMLRNumber(cell, worksheet);
    if (specialColVal.includes("1")) {
      tmpStr = checkMustHas(cell, worksheet);
    }
  }

  return tmpStr;
};

export default checkLandNSN;
