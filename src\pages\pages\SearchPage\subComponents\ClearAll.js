import React, {useContext} from "react";
import {StoreContext} from "../../../../store/StoreProvider";
import Act from "../../../../store/actions";

// material-ui
import { Button } from "@mui/material";

function ClearAll() {
    const [state, dispatch] = useContext(StoreContext);

    const handleClick = () => {
        dispatch({
            type: Act.SET_KEYWORD,
            payload: ""
        });
    }

    return (
    <div className="ClearAll">
      <Button variant="outlined" onClick={handleClick}>清空全部</Button>
    </div>
    );
}

export default ClearAll;
