import React, {useContext} from 'react';

// material ui
import {Box, FormControl, FormLabel, RadioGroup, FormControlLabel, Radio} from '@mui/material';
import {StoreContext} from "../../../../../store/StoreProvider";
import Act from "../../../../../store/actions";

function DateTypeSelect() {
    const [state, dispatch] = useContext(StoreContext);
    const selectDateType = [
        {value: "single", label: "單日"},
        {value: "multiple", label: "多日"}
    ];

    const handleChange = (event) => {
        dispatch({
            type: Act.SET_SELECTDATETYPE,
            payload: event.target.value
        });
    }

    return (
        <Box>
            <FormControl>
                <FormLabel>選擇搜尋日期範圍方式</FormLabel>
                <RadioGroup
                    row
                    defaultValue="single"
                >
                    {selectDateType.map((obj, index) => {
                        return (
                            <FormControlLabel
                                key={index}
                                value={obj.value}
                                control={
                                    <Radio
                                        onChange={handleChange}
                                    />
                                }
                                label={obj.label}
                            />
                        );
                    })}
                </RadioGroup>
            </FormControl>
        </Box>
    );
}

export default DateTypeSelect;