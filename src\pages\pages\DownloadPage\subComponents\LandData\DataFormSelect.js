import React, { useContext, useEffect, useState } from "react";

// material ui
import Grid from "@mui/material/Grid";
import FormControlLabel from "@mui/material/FormControlLabel";
import Radio from "@mui/material/Radio";
import RadioGroup from "@mui/material/RadioGroup";
import Typography from "@mui/material/Typography";

// utils
import { StoreContext } from "../../../../../store/StoreProvider";
import Act from "../../../../../store/actions";

function DataFormSelect() {
  const [state, dispatch] = useContext(StoreContext);
  const { landType, dataFormType } = state.download;
  const [landTypeStr, setLandTypeStr] = useState("");

  useEffect(() => {
    const findLandTypeSelect = landType.find((obj) => obj.select);
    if (findLandTypeSelect) {
      setLandTypeStr(findLandTypeSelect.property);
    }
  }, [landType]);

  const handleChange = (evt) => {
    let tmpDataFormType = JSON.parse(JSON.stringify(dataFormType));
    // init dataFormType
    tmpDataFormType = tmpDataFormType.map((el) => ({
      ...el,
      check: false,
    }));
    const findSelect = tmpDataFormType.find(
      (el) => el.type === evt.target.value
    );
    if (findSelect) {
      findSelect.check = true;
    }
    dispatch({
      type: Act.SET_DATAFORMTYPE,
      payload: tmpDataFormType,
    });
  };

  return (
    <Grid container mt={1}>
      <Grid item xs={12}>
        <Typography variant="h5">選擇搜尋方式</Typography>
      </Grid>
      <Grid item xs={12}>
        <RadioGroup defaultValue={dataFormType[0].type}>
          {landTypeStr &&
            dataFormType.map((obj, index) => (
              <FormControlLabel
                key={`type${index}`}
                value={obj.type}
                control={<Radio />}
                label={obj.label[landTypeStr]}
                onChange={handleChange}
              />
            ))}
        </RadioGroup>
      </Grid>
    </Grid>
  );
}

export default DataFormSelect;
