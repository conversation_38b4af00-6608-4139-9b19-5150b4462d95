import React, { useContext, useEffect, useState } from "react";

// 3rd party
import { useDropzone } from "react-dropzone";
import * as Excel from "exceljs";

// scss
import "./ImportDataPage.scss";

// material ui
import Box from "@mui/material/Box";
import Grid from "@mui/material/Grid";
import Input from "@mui/material/Input";
import Typography from "@mui/material/Typography";
import List from "@mui/material/List";
import ListItem from "@mui/material/ListItem";
import ListItemIcon from "@mui/material/ListItemIcon";
import ListItemText from "@mui/material/ListItemText";
import FolderIcon from "@mui/icons-material/Folder";

// components
import ImportDataBtn from "./subComponents/ImportDataBtn";

// store
import { StoreContext } from "../../../store/StoreProvider";
import Act from "../../../store/actions";

// firebase
import cloudStorage from "../../../api/firebase/cloudFirestore";

// utils
import { isEmpty } from "../../../utils";
import checkHeader from "./utils/excelHeader/checkHeader";
import checkBody from "./utils/excelBody/checkBody";

// config
import { checkRes, specRow } from "./config";

const errorMsg = [
  { errorCode: "too-many-files", chMsg: "上傳檔案超過一個" },
  { errorCode: "file-invalid-type", chMsg: "只接受副檔名為xlsx的檔案" },
];

function ImportDataPage() {
  const [, dispatch] = useContext(StoreContext);
  const [checkMsg, setCheckMsg] = useState(""); // 顯示上傳檔案名稱
  const [fileRejectMsg, setFileRejectMsg] = useState(""); // 顯示上傳檔案失敗後的錯誤訊息
  const [fbExHeader, setFbExHeader] = useState([]);

  const { acceptedFiles, getRootProps, getInputProps, fileRejections } =
    useDropzone({
      multiple: false, // 不能一次丟多個檔案
      // accepted MIME type，只接受副檔名: ".xlsx", ".xls"
      accept: [
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        "application/vnd.ms-excel",
      ],
      maxFiles: 1, // 上傳檔案數量總數最多一個
    });

  useEffect(() => {
    // get excelHeader info from firestore
    (async () => {
      setFbExHeader(await cloudStorage.getExcelHeader());
    })();
  }, []);

  useEffect(() => {
    // console.log("fbExHeader ", fbExHeader);
    // setFileName(acceptedFiles);
    if (isEmpty(acceptedFiles)) {
      dispatch({
        type: Act.SET_FILEDATA,
        payload: {},
      });
    }

    // 檢查表格header
    const promises = acceptedFiles.map(async (file) => {
      const workbook = new Excel.Workbook();
      await workbook.xlsx.load(file);
      let pass = checkRes.success;
      let fileRes = `${file.name} - `;
      workbook.eachSheet((worksheet) => {
        let resStr = "";
        // check header
        const headerRow = worksheet.getRow(1);
        // order
        let tmpRes = checkHeader.order.checkMethod(
          headerRow.values,
          fbExHeader
        );
        pass = pass === checkRes.success ? tmpRes : checkRes.failed;
        resStr += `${checkHeader.order[tmpRes]}。\n`;
        // header content
        tmpRes = checkHeader.content.checkMethod(headerRow.values, fbExHeader);
        pass = pass === checkRes.success ? tmpRes : checkRes.failed;
        resStr += `${checkHeader.content[tmpRes]}。\n`;

        /** record hasStartDate for each event
         *  lm: landMark
         *  lr: landRights
         * */
        let redStartDate = { lm: [], lr: [] };
        const { idIdx, startRow } = specRow;
        // check data body
        worksheet.eachRow((row, rowNumber) => {
          if (rowNumber >= startRow) {
            row.eachCell({ includeEmpty: true }, (cell, colNumber) => {
              const colId = worksheet.getRow(idIdx).getCell(colNumber).value;

              if (Object.hasOwn(checkBody, colId)) {
                let tmpStr = "";

                switch (colId) {
                  case "Land-->landName": {
                    if (cell.value) {
                      // reset record StartDate
                      redStartDate = { lm: [], lr: [] };
                      tmpStr = checkBody[colId].method(cell, worksheet);
                    }
                    break;
                  }
                  case "Land-->source": {
                    const checkList = [0, 1, 2, 3, 4, 5];
                    tmpStr = checkBody[colId].method(
                      cell,
                      worksheet,
                      checkList
                    );
                    break;
                  }
                  case "Land-->pawnRight":
                  case "Land-->plowingRight": {
                    const checkList = ["Y", "N"];
                    tmpStr = checkBody[colId].method(
                      cell,
                      worksheet,
                      checkList
                    );
                    break;
                  }
                  case "Land-->hasEvent-->LandMark-->hasStartDate-->DateEvent":
                  case "Land-->hasEvent-->LandRights-->hasStartDate-->DateEvent": {
                    tmpStr = checkBody[colId].method(
                      cell,
                      worksheet,
                      redStartDate
                    );
                    break;
                  }
                  default:
                    tmpStr = checkBody[colId].method(cell, worksheet);
                    break;
                }

                pass =
                  pass === checkRes.success && !tmpStr
                    ? checkRes.success
                    : checkRes.failed;
                resStr += tmpStr;
              }
            });
          }
        });
        // console.log("resStr ", resStr);
        // console.log("pass ", pass);
        fileRes +=
          pass === checkRes.success
            ? `表單: ${worksheet.name} 檢查結果正確。\n`
            : `表單: ${worksheet.name} 檢查結果有誤，請參照下列資訊修改資料，請修改表格資料後重新執行上傳。\n ${resStr}`;
      });
      return { res: fileRes, pass };
    });

    Promise.allSettled(promises)
      .then((data) => {
        if (!isEmpty(data)) {
          const { res, pass } = data[0].value;
          setCheckMsg(res);

          dispatch({
            type: Act.SET_FILEDATA,
            payload: pass === checkRes.failed ? {} : acceptedFiles[0],
          });
        }
      })
      .catch((err) => console.log(err));
  }, [acceptedFiles]);

  useEffect(() => {
    let tmpFileRejectMsg = "";
    if (fileRejections.length > 0) {
      tmpFileRejectMsg = errorMsg.find(
        (el) => el.errorCode === fileRejections[0].errors[0].code
      ).chMsg;
    }
    setFileRejectMsg(tmpFileRejectMsg);
  }, [fileRejections]);

  return (
    <Box className="ImportDataPage">
      <Grid container {...getRootProps({ className: "dropZone" })}>
        <Grid item xs={8}>
          <Input {...getInputProps()} />
          <Box className="typoBox">
            <Typography>請拖曳上傳檔案或點擊此區域選擇檔案</Typography>
          </Box>
        </Grid>
      </Grid>

      {!isEmpty(checkMsg) && (
        <Grid container>
          <List>
            <ListItem>
              <ListItemIcon>
                <FolderIcon />
              </ListItemIcon>
              <ListItemText>{checkMsg}</ListItemText>
            </ListItem>
            {fileRejectMsg !== "" && (
              <Typography style={{ color: "red" }}>{fileRejectMsg}</Typography>
            )}
          </List>
        </Grid>
      )}
      <Grid container className="bottomArea">
        <ImportDataBtn setCheckMsg={setCheckMsg} />
      </Grid>
    </Box>
  );
}

export default ImportDataPage;
