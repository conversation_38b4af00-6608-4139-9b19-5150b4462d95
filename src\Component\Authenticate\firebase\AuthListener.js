import { signInAnonymously, onAuthStateChanged } from "firebase/auth";
import { useContext, useEffect } from "react";

// store
import { StoreContext } from "../../../store/StoreProvider";
import act from "../../../store/actions";

// commons code
import { getFormatUser, isEmpty } from "../../../utils";

// firebase
import { getUser, setUser } from "./realtimeDatabase";

import role from "../../../config/App-role";
import itemConfig from "../../../api/config/config-localStorage";
import webConfig from "../../../api/config/config";
import { Api } from "../../../api/land/Api";

const AuthListener = ({ firebaseAuth }) => {
  // eslint-disable-next-line no-unused-vars
  const [state, dispatch] = useContext(StoreContext);
  const { main, user } = state;
  const { production, refreshAnonymousToken } = main;
  const { role: userRole } = user;

  useEffect(() => {
    // firebase 開啟 production 及 匿名登入時, 使用下列方法進行身分驗證
    // 如果signInAnonymously方法正確完成，將觸發在onAuthStateChanged註冊的觀察者
    if (production === true || production === "true") {
      // sign in 後不用 refresh anonymousToken
      // sign out 後才要 refresh anonymousToken
      if (
        !refreshAnonymousToken ||
        localStorage.getItem(itemConfig.isLogin) ||
        localStorage.getItem(itemConfig.token)
      )
        return;

      if (webConfig.ENABLE_ANONYMOUS) {
        signInAnonymously(firebaseAuth)
          .then(() => {
            // console.log("signInAnonymously");
            // Signed in..
          })
          .catch((error) => {
            console.error(error);
          });
      }
    } else if (production === false || production === "false") {
      if (userRole === role.anonymous) {
        // localStorage
        localStorage.removeItem(itemConfig.isLogin);
        localStorage.removeItem(itemConfig.token);
      }
    }
  }, [production, refreshAnonymousToken]);

  useEffect(() => {
    if (!firebaseAuth) {
      return;
    }

    onAuthStateChanged(firebaseAuth, async (userData) => {
      // 若 firebase 開啟匿名登入, 則 user 有資料, 反之, user 為 null
      if (userData) {
        const userInfo = getFormatUser(userData);
        const { uid, displayName, email } = userInfo;
        // 若 firebase 開啟匿名登入, 未登入狀態,
        // user.isAnonymous 為 true. userInfo 會帶 token, token 用於 API authorization
        if (userData.isAnonymous) {
          dispatch({
            type: act.FIREBASE_LOGIN_USER,
            payload: { currentUser: userData, ...userInfo },
          });
        } else if (uid && (displayName || email)) {
          dispatch({
            type: act.FIREBASE_LOGIN_USER,
            payload: { currentUser: userData, ...userInfo },
          });
        }
        // localStorage
        localStorage.setItem(itemConfig.isLogin, JSON.stringify(true));

        // 與API溝通，header帶token資訊
        const token = await userData.getIdToken();
        Api.setAxiosAuth(token);

        // 若 user 已登入, check realtime DB 是否該使用者的資料, 若沒有, 則將 user 資料寫入
        const data = await getUser(uid);
        if (isEmpty(data)) {
          // 新增 role key
          const _userInfo = { ...userInfo, role: role.anonymous };
          // 有些參數為 undefined, 無法存入，在此將被過濾掉
          const newUserInfo = JSON.parse(JSON.stringify(_userInfo));
          await setUser(uid, newUserInfo);
          dispatch({
            type: act.FIREBASE_LOGIN_USER,
            payload: newUserInfo,
          });
        } else {
          dispatch({
            type: act.FIREBASE_LOGIN_USER,
            payload: {
              ...userInfo,
              role: data.role,
            },
          });
        }
      } else {
        dispatch({
          type: act.FIREBASE_LOGOUT_USER,
        });
        // localStorage
        localStorage.removeItem(itemConfig.isLogin);
      }
    });
  }, [firebaseAuth, dispatch]);

  return null;
};

export default AuthListener;
