import React, { useContext, useState, useEffect } from "react";
import NumberFormat from "react-number-format";

// material ui
import TextField from "@mui/material/TextField";

// utils
import { disableEditFunction, disableFontColor } from "../../../common";
import {
  landMarkPlaceHolder,
  landRightsPlaceHolder,
} from "../../../common/phDes";
import { StoreContext } from "../../../../../../store/StoreProvider";
import { isEmpty } from "../../../../../../utils";
import evtType from "../../../../../../utils/evtType";

function StartDateInput({ info, handleChange, index, type }) {
  const [state] = useContext(StoreContext);
  const { landMarkInfo, landRightInfo } = state.edit;
  const [checkDateRepeat, setCheckDateRepeat] = useState([]); // 判斷有無重複日期
  const [landEvtInfo, setLandEvtInfo] = useState([]);
  const [helperText, setHelperText] = useState("");
  const [plArr, setPlArr] = useState({});

  useEffect(() => {
    if (type === evtType.LandMark) {
      setLandEvtInfo(landMarkInfo);
      setHelperText("土地標示變更登記日期重複");
      setPlArr(landMarkPlaceHolder);
    } else if (type === evtType.LandRights) {
      setLandEvtInfo(landRightInfo);
      setHelperText("土地權利變更登記日期重複");
      setPlArr(landRightsPlaceHolder);
    }
  }, [type]);

  useEffect(() => {
    if (isEmpty(landEvtInfo)) return;
    // 初始化checkDateRepeat
    const tmpArr = [];
    for (let i = 0; i < landEvtInfo.length; i += 1) {
      tmpArr.push(false);
    }
    setCheckDateRepeat(tmpArr);
  }, [landEvtInfo]);

  return (
    <NumberFormat
      disabled={disableEditFunction(state)}
      value={info.value ? info.value.split("-")[0] : info.value}
      variant="standard"
      fullWidth
      customInput={TextField}
      format="####"
      placeholder={plArr[info.predicate]}
      onValueChange={(values) => {
        const { value } = values;
        const newData = { ...info, index };
        const newValue = value ? `${value}-00-00` : "";
        handleChange(newValue, newData);
      }}
      inputProps={disableFontColor}
      error={checkDateRepeat[index]}
      helperText={checkDateRepeat[index] ? helperText : ""}
    />
  );
}

export default StartDateInput;
