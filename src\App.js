// React
import React, { useEffect } from "react";
import { BrowserRouter } from "react-router-dom";

// store
import axios from "axios";
import StoreProvider from "./store/StoreProvider";

// pages
import Header from "./pages/main/Header";
import Body from "./pages/main/Body";
// import Footer from "./pages/main/Footer";

// Component
import { FirebaseLayer } from "./Component/Authenticate/FirebaseLayer";
import { addAxiosInterceptor } from "./api/land/Api";

// scss
import "./App.scss";

const millisToSS = (millis) => millis / 1000;

const App = () => {
  useEffect(() => {
    // add interceptor to global axios
    addAxiosInterceptor(axios, millisToSS);
  }, []);

  return (
    <>
      {/* 使用 BrowserRouter 建立 history object */}
      <BrowserRouter>
        {/* 在最上層提供 global store */}
        <StoreProvider>
          <FirebaseLayer>
            {/* 載入資料相關區 */}
            {/* <DataLayer /> */}
            <div className="App">
              {/* 頁面頂層連結區, 如要變更內容請至 APP-layout.js */}
              <Header />
              {/* 頁面跳轉處理區, 如要變更內容請至 APP-layout.js */}
              <Body />

              {/* <Footer /> */}
            </div>
          </FirebaseLayer>
        </StoreProvider>
      </BrowserRouter>
    </>
  );
};

export default App;
