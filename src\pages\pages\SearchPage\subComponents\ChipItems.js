import React, { useContext } from "react";
import { Chip, Grid } from "@mui/material";
import { StoreContext } from "../../../../store/StoreProvider";
import Act from "../../../../store/actions";

function ChipItems() {
  const [state, dispatch] = useContext(StoreContext);
  const { searchColList } = state.search;

  const handleDelete = (label) => {
    const tmpSearchColList = JSON.parse(JSON.stringify(searchColList));
    const findColName = tmpSearchColList.find(
      (element) => element.label === label
    );
    if (findColName) {
      findColName.select = false;
    }

    dispatch({
      type: Act.SET_SEARCHCOLLIST,
      payload: tmpSearchColList,
    });
  };

  return (
    <Grid item xs={11}>
      {searchColList
        .filter((item) => item.select)
        .map((item, index) => (
          <Chip
            style={{ marginRight: "10px", marginBottom: "10px" }}
            item={item}
            key={index}
            color="primary"
            variant="outlined"
            label={item.label}
            onDelete={() => handleDelete(item.label)}
          />
        ))}
    </Grid>
  );
}

export default ChipItems;
