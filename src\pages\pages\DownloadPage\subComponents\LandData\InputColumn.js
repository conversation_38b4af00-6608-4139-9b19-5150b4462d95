import React, { useContext, useState, useEffect } from "react";

// material ui
import Autocomplete from "@mui/material/Autocomplete";
import Grid from "@mui/material/Grid";
import TextField from "@mui/material/TextField";
import Typography from "@mui/material/Typography";

// utils
import { StoreContext } from "../../../../../store/StoreProvider";
import { Api, readOntoData } from "../../../../../api/land/Api";
import Act from "../../../../../store/actions";

function InputColumn() {
  const [state, dispatch] = useContext(StoreContext);
  const { dataFormType, dwLandName, startYear, landType } = state.download;
  const [LNList, setLNList] = useState([]);
  const [LSNList, setLSNList] = useState([]);
  const [tmpFormType, setTmpFormType] = useState(""); // type3不顯示結束年份輸入欄位
  const [yearList, setYearList] = useState([]);

  useEffect(() => {
    const findObj = dataFormType.find((el) => el.check);
    if (findObj) {
      setTmpFormType(findObj.type);
    } else {
      setTmpFormType("");
    }
  }, [dataFormType]);

  useEffect(() => {
    const apiStr = Api.getLNList();
    let mounted = true;
    readOntoData(apiStr).then((result) => {
      if (mounted) {
        setLNList(result.data);
      }
    });

    return () => {
      mounted = false;
    };
  }, []);

  useEffect(() => {
    if (dwLandName === "" || dwLandName === null) return;
    const apiStr = Api.getLSList().replace("{landName}", dwLandName);
    readOntoData(apiStr).then((result) => {
      setLSNList(result.data);
    });
  }, [dwLandName]);

  useEffect(() => {
    const selectType = landType.find((el) => el.select);
    if (!dwLandName || !selectType) return;
    const tmpType = selectType.property;
    const apiStr = Api.getLandYearList()
      .replace("{landName}", dwLandName)
      .replace("{type}", tmpType);

    readOntoData(apiStr).then((res) => {
      const numPattern = /[0-9]/g;
      const yearRes = res.data.filter(({ year }) => year.match(numPattern));
      const tmpArr = [];
      if (res.data.length > 0) {
        const minYear = yearRes[0].year;
        const maxYear = yearRes.at(-1).year;

        for (let i = minYear; i <= maxYear; i++) {
          tmpArr.push(i.toString());
        }
      }
      setYearList(tmpArr);
    });
  }, [landType, dwLandName]);

  const setLandName = (event, value) => {
    dispatch({
      type: Act.SET_DWLANDNAME,
      payload: value,
    });
  };

  const setLandSerialNumber = (event, value) => {
    dispatch({
      type: Act.SET_DWLANDSERIALNUMBER,
      payload: value,
    });
  };

  return (
    <Grid container flexDirection="column">
      <Grid item xs={12}>
        <Typography variant="h5">搜尋地區與時間</Typography>
      </Grid>
      <Grid
        item
        xs={5}
        style={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          margin: "5px 0",
        }}
      >
        <Grid item xs={1}>
          <Typography>空間範圍</Typography>
        </Grid>
        <Grid item xs={tmpFormType === "type2" ? 5 : 10}>
          <Autocomplete
            options={LNList.map((el) => el.landName)}
            renderInput={(params) => (
              <TextField {...params} label="土名（舊地段名）" />
            )}
            onChange={setLandName}
          />
        </Grid>
        {tmpFormType === "type2" && (
          <Grid item xs={5}>
            <Autocomplete
              options={LSNList.map((element) => element.landSerialNumber)}
              renderInput={(params) => (
                <TextField {...params} label="地番（地號）" />
              )}
              onChange={setLandSerialNumber}
            />
          </Grid>
        )}
      </Grid>
      <Grid
        container
        item
        xs={5}
        mb={1}
        justifyContent="space-between"
        alignItems="center"
      >
        <Grid item xs={1}>
          <Typography>時間範圍</Typography>
        </Grid>
        <Grid item xs={tmpFormType !== "type3" ? 5 : 10}>
          <Autocomplete
            options={yearList}
            renderInput={(params) => <TextField {...params} label="開始年分" />}
            onChange={(evt, data) => {
              dispatch({
                type: Act.SET_STARTYEAR,
                payload: data,
              });
            }}
            disabled={!dwLandName}
          />
        </Grid>
        {tmpFormType !== "type3" && (
          <Grid item xs={5}>
            <Autocomplete
              options={yearList}
              renderInput={(params) => (
                <TextField {...params} label="結束年分" />
              )}
              onChange={(evt, data) => {
                if (data <= startYear || startYear === "") return;
                dispatch({
                  type: Act.SET_ENDYEAR,
                  payload: data,
                });
              }}
              disabled={!dwLandName}
            />
          </Grid>
        )}
      </Grid>
    </Grid>
  );
}

export default InputColumn;
