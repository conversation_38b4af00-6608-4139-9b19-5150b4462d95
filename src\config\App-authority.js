import role from "./App-role";

const authority = {
  Home: [role.admin, role.editor, role.reader, role.developer, role.anonymous],
  SignIn: [
    role.admin,
    role.editor,
    role.reader,
    role.developer,
    role.anonymous,
  ],
  SignOut: [
    role.admin,
    role.editor,
    role.reader,
    role.developer,
    role.anonymous,
  ],
  // Signup: [role.admin, role.editor, role.reader, role.developer],
  Search: [role.admin, role.editor, role.developer],
  Edit: [role.admin, role.editor, role.developer],
  Download: [role.admin, role.developer],
  Authority: [role.admin, role.developer],
  Admin: [role.admin, role.developer],
  ImportData: [role.admin, role.developer],
  History: [role.admin, role.developer],
  Statistics: [role.admin, role.developer],
  Gis: [role.admin, role.developer],
};

export default authority;
