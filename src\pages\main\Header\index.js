import React, { useState, useEffect, useContext } from "react";

// material ui
import Box from "@mui/material/Box";
import Button from "@mui/material/Button";

// store
import { Link, useLocation } from "react-router-dom";
import { StoreContext } from "../../../store/StoreProvider";
import {
  getVisibleLeftMenus,
  getVisibleRightMenus,
} from "../../../utils/headerPermissions";

// css
import "./header.scss";

const Header = () => {
  // store
  const [state] = useContext(StoreContext);
  const { user } = state;
  const { role } = user;
  // get isLogin from localStorage
  const isLogin = JSON.parse(localStorage.getItem("land-isLogin"));
  const location = useLocation();
  const [activePage, setActivePage] = useState("");

  // 獲取可見的菜單項目
  const visibleLeftMenus = getVisibleLeftMenus(role);
  const visibleRightMenus = getVisibleRightMenus(role, isLogin);

  useEffect(() => {
    const findPage = visibleLeftMenus.find(
      (menu) => menu.path === location.pathname
    );
    if (findPage) {
      setActivePage(findPage.id);
    }
  }, [role, visibleLeftMenus, location.pathname]);

  return (
    <div className="Header">
      <Box className="Box">
        {visibleLeftMenus.map((item) => (
          <Link to={item.path} key={item.id} style={{ textDecoration: "none" }}>
            <div className="HeaderItem">
              <Button variant="text" onClick={() => setActivePage(item.id)}>
                {item.name}
              </Button>
              {activePage === item.id && <div className="hrLine" />}
            </div>
          </Link>
        ))}
      </Box>
      <Box className="Box">
        {visibleRightMenus.map((item) => (
          <Link to={item.path} key={item.id} style={{ textDecoration: "none" }}>
            <div className="HeaderItem">
              <Button variant="text" onClick={() => setActivePage("")}>
                {item.name}
              </Button>
            </div>
          </Link>
        ))}
      </Box>
    </div>
  );
};

export default Header;
