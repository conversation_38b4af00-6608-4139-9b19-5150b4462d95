<?xml version="1.0" encoding="UTF-8"?>
<WebElementEntity>
   <description></description>
   <name>input_Email_email</name>
   <tag></tag>
   <elementGuidId>e02621f4-8af9-449d-ae30-485eaee4e5ea</elementGuidId>
   <selectorCollection>
      <entry>
         <key>CSS</key>
         <value>#ui-sign-in-email-input</value>
      </entry>
      <entry>
         <key>XPATH</key>
         <value>//input[@id='ui-sign-in-email-input']</value>
      </entry>
   </selectorCollection>
   <selectorMethod>XPATH</selectorMethod>
   <smartLocatorCollection>
      <entry>
         <key>SMART_LOCATOR</key>
         <value>internal:label=&quot;Email&quot;i</value>
      </entry>
   </smartLocatorCollection>
   <smartLocatorEnabled>false</smartLocatorEnabled>
   <useRalativeImagePath>true</useRalativeImagePath>
   <webElementProperties>
      <isSelected>false</isSelected>
      <matchCondition>equals</matchCondition>
      <name>tag</name>
      <type>Main</type>
      <value>input</value>
      <webElementGuid>a6eb582b-cad1-485c-a1b1-45ec957b426e</webElementGuid>
   </webElementProperties>
   <webElementProperties>
      <isSelected>true</isSelected>
      <matchCondition>equals</matchCondition>
      <name>type</name>
      <type>Main</type>
      <value>email</value>
      <webElementGuid>da6044d3-f813-43ab-8bfc-4e7a47a28311</webElementGuid>
   </webElementProperties>
   <webElementProperties>
      <isSelected>true</isSelected>
      <matchCondition>equals</matchCondition>
      <name>name</name>
      <type>Main</type>
      <value>email</value>
      <webElementGuid>805e972b-8eca-4205-ab69-e85c5cde5c52</webElementGuid>
   </webElementProperties>
   <webElementProperties>
      <isSelected>true</isSelected>
      <matchCondition>equals</matchCondition>
      <name>id</name>
      <type>Main</type>
      <value>ui-sign-in-email-input</value>
      <webElementGuid>ee545cbc-788b-4969-8882-9f81767f4d55</webElementGuid>
   </webElementProperties>
   <webElementProperties>
      <isSelected>false</isSelected>
      <matchCondition>equals</matchCondition>
      <name>autocomplete</name>
      <type>Main</type>
      <value>username</value>
      <webElementGuid>ca618fc9-6e31-462e-801f-7f7784a737df</webElementGuid>
   </webElementProperties>
   <webElementProperties>
      <isSelected>false</isSelected>
      <matchCondition>equals</matchCondition>
      <name>class</name>
      <type>Main</type>
      <value>mdl-textfield__input firebaseui-input firebaseui-id-email</value>
      <webElementGuid>1784f8a1-2a58-4bc9-b5ed-30f2fadb97c1</webElementGuid>
   </webElementProperties>
   <webElementProperties>
      <isSelected>false</isSelected>
      <matchCondition>equals</matchCondition>
      <name>xpath</name>
      <type>Main</type>
      <value>id(&quot;ui-sign-in-email-input&quot;)</value>
      <webElementGuid>7012e302-bf53-4af1-b63a-93bddc63464c</webElementGuid>
   </webElementProperties>
   <webElementXpaths>
      <isSelected>true</isSelected>
      <matchCondition>equals</matchCondition>
      <name>xpath:attributes</name>
      <type>Main</type>
      <value>//input[@id='ui-sign-in-email-input']</value>
      <webElementGuid>8041f510-4cbb-4283-868e-a696716dff4e</webElementGuid>
   </webElementXpaths>
   <webElementXpaths>
      <isSelected>false</isSelected>
      <matchCondition>equals</matchCondition>
      <name>xpath:idRelative</name>
      <type>Main</type>
      <value>//div[@id='firebaseui_container']/div/form/div[2]/div/div/input</value>
      <webElementGuid>d46bf660-c50f-4449-9e83-ef96a9df859e</webElementGuid>
   </webElementXpaths>
   <webElementXpaths>
      <isSelected>false</isSelected>
      <matchCondition>equals</matchCondition>
      <name>xpath:position</name>
      <type>Main</type>
      <value>//input</value>
      <webElementGuid>00e4e7c0-a36b-4363-9113-3f2092c566cd</webElementGuid>
   </webElementXpaths>
   <webElementXpaths>
      <isSelected>false</isSelected>
      <matchCondition>equals</matchCondition>
      <name>xpath:customAttributes</name>
      <type>Main</type>
      <value>//input[@type = 'email' and @name = 'email' and @id = 'ui-sign-in-email-input']</value>
      <webElementGuid>ad8fd30d-b86c-4760-af4a-5f87cf8dd4c8</webElementGuid>
   </webElementXpaths>
</WebElementEntity>
