<?xml version="1.0" encoding="UTF-8"?>
<WebElementEntity>
   <description></description>
   <name>input_Password_password</name>
   <tag></tag>
   <elementGuidId>0d1b9914-8449-4d65-8841-7091ea218743</elementGuidId>
   <selectorCollection>
      <entry>
         <key>CSS</key>
         <value>#ui-sign-in-password-input</value>
      </entry>
      <entry>
         <key>XPATH</key>
         <value>//input[@id='ui-sign-in-password-input']</value>
      </entry>
   </selectorCollection>
   <selectorMethod>XPATH</selectorMethod>
   <smartLocatorCollection>
      <entry>
         <key>SMART_LOCATOR</key>
         <value>internal:label=&quot;Password&quot;i</value>
      </entry>
   </smartLocatorCollection>
   <smartLocatorEnabled>false</smartLocatorEnabled>
   <useRalativeImagePath>true</useRalativeImagePath>
   <webElementProperties>
      <isSelected>false</isSelected>
      <matchCondition>equals</matchCondition>
      <name>tag</name>
      <type>Main</type>
      <value>input</value>
      <webElementGuid>1c7d71de-363d-4294-81ef-189b5fce7306</webElementGuid>
   </webElementProperties>
   <webElementProperties>
      <isSelected>true</isSelected>
      <matchCondition>equals</matchCondition>
      <name>type</name>
      <type>Main</type>
      <value>password</value>
      <webElementGuid>9c34af54-8e6c-45b9-81b2-dd388e39dfc9</webElementGuid>
   </webElementProperties>
   <webElementProperties>
      <isSelected>true</isSelected>
      <matchCondition>equals</matchCondition>
      <name>name</name>
      <type>Main</type>
      <value>password</value>
      <webElementGuid>d7560e2a-aad5-497e-b8bf-06ec8772df0c</webElementGuid>
   </webElementProperties>
   <webElementProperties>
      <isSelected>true</isSelected>
      <matchCondition>equals</matchCondition>
      <name>id</name>
      <type>Main</type>
      <value>ui-sign-in-password-input</value>
      <webElementGuid>0d28e4be-9d58-4ce1-bd7b-d539dfe4401d</webElementGuid>
   </webElementProperties>
   <webElementProperties>
      <isSelected>false</isSelected>
      <matchCondition>equals</matchCondition>
      <name>autocomplete</name>
      <type>Main</type>
      <value>current-password</value>
      <webElementGuid>3d2fe54b-065f-4ebe-b53d-8f6ebb6f59b8</webElementGuid>
   </webElementProperties>
   <webElementProperties>
      <isSelected>false</isSelected>
      <matchCondition>equals</matchCondition>
      <name>class</name>
      <type>Main</type>
      <value>mdl-textfield__input firebaseui-input firebaseui-id-password</value>
      <webElementGuid>d9f9e487-7d32-4ed3-9ebc-5657060a29f0</webElementGuid>
   </webElementProperties>
   <webElementProperties>
      <isSelected>false</isSelected>
      <matchCondition>equals</matchCondition>
      <name>xpath</name>
      <type>Main</type>
      <value>id(&quot;ui-sign-in-password-input&quot;)</value>
      <webElementGuid>1d544757-f46a-4706-930a-8fa854b9d308</webElementGuid>
   </webElementProperties>
   <webElementXpaths>
      <isSelected>true</isSelected>
      <matchCondition>equals</matchCondition>
      <name>xpath:attributes</name>
      <type>Main</type>
      <value>//input[@id='ui-sign-in-password-input']</value>
      <webElementGuid>c8d14476-627e-494e-8392-82c4e72e192a</webElementGuid>
   </webElementXpaths>
   <webElementXpaths>
      <isSelected>false</isSelected>
      <matchCondition>equals</matchCondition>
      <name>xpath:idRelative</name>
      <type>Main</type>
      <value>//div[@id='firebaseui_container']/div/form/div[2]/div[3]/input</value>
      <webElementGuid>0bed98e4-b1cd-4d90-b289-d2e339b978f4</webElementGuid>
   </webElementXpaths>
   <webElementXpaths>
      <isSelected>false</isSelected>
      <matchCondition>equals</matchCondition>
      <name>xpath:position</name>
      <type>Main</type>
      <value>//div[3]/input</value>
      <webElementGuid>7c8cec9d-bef9-4b54-8147-6c56739c293e</webElementGuid>
   </webElementXpaths>
   <webElementXpaths>
      <isSelected>false</isSelected>
      <matchCondition>equals</matchCondition>
      <name>xpath:customAttributes</name>
      <type>Main</type>
      <value>//input[@type = 'password' and @name = 'password' and @id = 'ui-sign-in-password-input']</value>
      <webElementGuid>674ae609-1f2d-466d-8354-52c923b5f077</webElementGuid>
   </webElementXpaths>
</WebElementEntity>
