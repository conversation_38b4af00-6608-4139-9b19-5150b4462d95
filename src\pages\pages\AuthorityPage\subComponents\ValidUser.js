import React, {useContext} from 'react';
import {
    Checkbox,
    Paper,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow
} from "@mui/material";
import {StoreContext} from "../../../../store/StoreProvider";
import Act from "../../../../store/actions";
import {updateUser} from "../../../../Component/Authenticate/firebase/realtimeDatabase";

function ValidUser() {
    const [state, dispatch] = useContext(StoreContext);
    const { userInfo, roles } = state.authority;

    const handleClick = (user, role) => {
        const tmpUserInfo = JSON.parse(JSON.stringify(userInfo));
        const findUser = tmpUserInfo.find(element => element.uid === user.uid);
        if (findUser) {
            const tmpRole = findUser.role.split(",");
            if (findUser.role.indexOf(role.toLowerCase()) >= 0) {
                // cancel role
                const tmpIndex = tmpRole.indexOf(role.toLowerCase());
                tmpRole.splice(tmpIndex, 1)
            } else {
                // add role
                tmpRole.push(role.toLowerCase());
            }
            findUser.role = tmpRole.join();
            dispatch({
                type: Act.SET_USERINFO,
                payload: tmpUserInfo
            });

            // update firebase
            updateUser(findUser.uid, findUser);
        }
    }

    return (
        <TableContainer component={Paper} className="ValidUser">
            <Table>
                <TableHead className="tableHead">
                    <TableRow>
                        <TableCell align="center"></TableCell>
                        {roles.map((role,index) => (
                            <TableCell key={index} align="center">{role}</TableCell>
                        ))}
                    </TableRow>
                </TableHead>
                <TableBody>
                    {userInfo
                        .filter(info => {
                            let exist = false;
                            roles.forEach(role => {
                                if (info.role.indexOf(role.toLowerCase()) >= 0) {
                                    exist = true;
                                }
                            })
                            return exist;
                        })
                        .map((user, index) => {
                        return (
                            <TableRow key={index}>
                                <TableCell align="center">{user.displayName}</TableCell>
                                {roles.map((role, index) => {
                                    return (
                                        <TableCell key={index} align="center">
                                            <Checkbox
                                                color="default"
                                                checked={user.role.indexOf(role.toLowerCase()) >= 0}
                                                onClick={() => handleClick(user, role)}
                                            />
                                        </TableCell>
                                    );
                                })}
                            </TableRow>
                        );
                    })}
                </TableBody>
            </Table>
        </TableContainer>
    );
}

export default ValidUser;