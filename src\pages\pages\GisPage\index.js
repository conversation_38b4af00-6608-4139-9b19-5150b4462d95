import React, { useEffect, useState, useCallback, useMemo } from "react";
import "./GisPage.scss";

import { Api, updateOntoData, readOntoData } from "../../../api/land/Api";
import {
    actualCoordinateConversion,
    autoDetectPKM,
    isValidCoordinate,
    boundIngBoxex
} from "../../pages/EditPage/common/computedColumnValue";
import SaveModal from "./subComponents/SaveModal";

// MUI
import {
    Box,
    TextField,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    Paper,
    Button,
    MenuItem,
    Pagination,
    InputAdornment,
    Select,
    FormControl,
    Typography,
    CircularProgress,
    Alert,
    Snackbar
} from "@mui/material";
import SearchIcon from "@mui/icons-material/Search";

// 常數配置
const PAGINATION_OPTIONS = [10, 25, 50];
const COORDINATE_BOUNDS = {
    x: { min: boundIngBoxex.all.minX, max: boundIngBoxex.all.maxX },
    y: { min: boundIngBoxex.all.minY, max: boundIngBoxex.all.maxY }
};

function GisPage() {
    const [rows, setRows] = useState([]);
    const [originalRows, setOriginalRows] = useState([]);
    const [searchTerm, setSearchTerm] = useState("");
    const [editedRows, setEditedRows] = useState({});
    const [rowsPerPage, setRowsPerPage] = useState(10);
    const [page, setPage] = useState(1);
    const [loading, setLoading] = useState(false);
    const [saving, setSaving] = useState(false);
    const [saveModalOpen, setSaveModalOpen] = useState(false);
    const [errors, setErrors] = useState({});
    const [notification, setNotification] = useState({ open: false, message: "", severity: "info" });

    // 工具函數：顯示通知
    const showNotification = useCallback((message, severity = "info") => {
        setNotification({ open: true, message, severity });
    }, []);

    // 工具函數：關閉通知
    const handleCloseNotification = useCallback(() => {
        setNotification(prev => ({ ...prev, open: false }));
    }, []);

    // 工具函數：驗證座標值
    const validateValue = useCallback((field, value) => {
        const num = parseFloat(value);
        if (isNaN(num)) return true;

        const bounds = COORDINATE_BOUNDS[field];
        return bounds ? (num < bounds.min || num > bounds.max) : false;
    }, []);

    // 工具函數：查找行數據
    const findRowById = useCallback((id) => {
        return rows.find(row => row.id === id);
    }, [rows]);

    const getTableData = useCallback(async () => {
        setLoading(true);
        try {
            const res = await readOntoData(Api.getGisTableData());
            const data = res?.data || [];
            setRows(data);
            setOriginalRows(data);
            // 清空編輯狀態
            setEditedRows({});
            setErrors({});
        } catch (err) {
            console.error("載入資料失敗:", err);
            showNotification("載入資料失敗，請重新整理頁面", "error");
        } finally {
            setLoading(false);
        }
    }, [showNotification]);

    useEffect(() => {
        getTableData();
    }, []);

    // 更新經緯度
    const handleLatLngChange = useCallback((id, field, value) => {
        // 判斷數值是否符合範圍
        const isError = validateValue(field, value);
        setErrors((prev) => ({
            ...prev,
            [`${id}-${field}`]: isError
        }));

        // 查找當前行數據（只查找一次）
        const currentRow = findRowById(id);
        if (!currentRow) return;

        const currentX = field === "x" ? value : currentRow.x;
        const currentY = field === "y" ? value : currentRow.y;
        const tempKey = field === 'x' ? 'y' : 'x';

        // 準備更新的數據
        let updateData = { [field]: value };
        let editedData = {
            [field]: value,
            [tempKey]: currentRow[tempKey],
            landName: currentRow.landName,
            landSerialNumber: currentRow.landSerialNumber,
        };

        // 如果座標有效，進行座標轉換
        if (isValidCoordinate(currentX) && isValidCoordinate(currentY)) {
            const isPKM = autoDetectPKM(currentX, currentY);
            const conversionResult = actualCoordinateConversion(currentX, currentY, isPKM);

            if (conversionResult) {
                updateData = { ...updateData, ...conversionResult };
                editedData = { ...editedData, ...conversionResult };
            }
        }

        // 一次性更新 rows
        setRows((prevRows) =>
            prevRows.map((row) =>
                row.id === id ? { ...row, ...updateData } : row
            )
        );

        // 一次性更新 editedRows
        setEditedRows((prev) => ({
            ...prev,
            [id]: {
                ...prev[id],
                ...editedData,
            },
        }));
    }, [validateValue, findRowById]);

    // 篩選
    const filteredRows = useMemo(() => {
        if (!searchTerm.trim()) return rows;

        const keyword = searchTerm.toLowerCase();
        return rows.filter((row) => {
            const searchFields = [
                row.landName,
                row.landSerialNumber,
                row.x?.toString(),
                row.y?.toString(),
                row.lat?.toString(),
                row.long?.toString()
            ];

            return searchFields.some(field =>
                field?.toLowerCase().includes(keyword)
            );
        });
    }, [rows, searchTerm]);

    // 分頁後資料
    const paginatedRows = useMemo(() => {
        const startIndex = (page - 1) * rowsPerPage;
        return filteredRows.slice(startIndex, startIndex + rowsPerPage);
    }, [filteredRows, page, rowsPerPage]);

    // 批量更新資料的函數
    const updateLandDataBatch = async (data) => {
        const results = [];

        for (const [key, coords] of Object.entries(data)) {
            try {
                const originalRow = originalRows.find((row) => row.id === key);
                if (!originalRow) {
                    throw new Error(`找不到 ID 為 ${key} 的原始資料`);
                }

                const payload = {
                    entrySrc: {
                        graph: "south",
                        classType: "Land",
                        value: {
                            twd97_x: originalRow.x,
                            twd97_y: originalRow.y,
                            wgs84_lat: originalRow.lat,
                            wgs84_long: originalRow.long,

                        },
                        srcId: key
                    },
                    entryDst: {
                        graph: "south",
                        classType: "Land",
                        value: {
                            twd97_x: coords.hasOwnProperty("x")
                                ? coords.x
                                : originalRow.x,
                            twd97_y: coords.hasOwnProperty("y")
                                ? coords.y
                                : originalRow.y,
                            wgs84_lat: coords.hasOwnProperty("lat")
                                ? coords.lat
                                : originalRow.lat,
                            wgs84_long: coords.hasOwnProperty("long")
                                ? coords.long
                                : originalRow.long,
                        },
                        srcId: key
                    }
                };

                const res = await updateOntoData(
                    Api.restfulCRUD(),
                    payload.entrySrc,
                    payload.entryDst,
                    -1,
                    0
                );

                if (!res?.state) {
                    throw new Error(`API 回應狀態異常: ${res?.statusText || '未知錯誤'}`);
                }

                results.push({ id: key, success: true });
            } catch (error) {
                console.error(`更新 ${key} 失敗:`, error);
                results.push({ id: key, success: false, error: error.message });
            }
        }

        return results;
    };

    // 取消變更
    const handleCancel = useCallback(() => {
        setRows([...originalRows]);
        setEditedRows({});
        setErrors({});
        showNotification("已取消所有變更", "success");
    }, [originalRows, showNotification]);

    if (loading) {
        return (
            <div className="mainBox_shadowMain GisPage" style={{
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                minHeight: '400px'
            }}>
                <CircularProgress />
            </div>
        );
    }

    return (
        <div className="mainBox_shadowMain GisPage">
            {/* 搜尋欄 */}
            <Box className="TopArea" sx={{ mb: 2 }}>
                <TextField
                    placeholder="搜尋地段、地號、經緯度..."
                    variant="outlined"
                    size="small"
                    fullWidth
                    value={searchTerm}
                    onChange={(e) => {
                        setSearchTerm(e.target.value);
                        setPage(1);
                    }}
                    InputProps={{
                        startAdornment: (
                            <InputAdornment position="start">
                                <SearchIcon />
                            </InputAdornment>
                        ),
                    }}
                />
            </Box>

            {/* 表格控制區 */}
            <Box
                sx={{
                    display: "flex",
                    justifyContent: "space-between",
                    mb: 1,
                    alignItems: "center",
                }}
            >
                <Typography variant="body2">
                    共找到 <strong style={{ color: '#1976d2' }}>{filteredRows.length}</strong> 筆資料
                    {Object.keys(editedRows).length > 0 && (
                        <span style={{ color: '#ff9800', marginLeft: '10px' }}>
                            (有 {Object.keys(editedRows).length} 筆未儲存的變更)
                        </span>
                    )}
                </Typography>

                <FormControl size="small" sx={{ minWidth: 120 }}>
                    <Select
                        value={rowsPerPage}
                        onChange={(e) => {
                            setRowsPerPage(e.target.value);
                            setPage(1);
                        }}
                    >
                        {PAGINATION_OPTIONS.map(option => (
                            <MenuItem key={option} value={option}>
                                每頁顯示 {option} 筆
                            </MenuItem>
                        ))}
                    </Select>
                </FormControl>
            </Box>

            {/* 表格 */}
            <Box className="BtmArea">
                <TableContainer component={Paper}>
                    <Table sx={{ border: "1px solid #ddd" }}>
                        <TableHead>
                            <TableRow sx={{ backgroundColor: "#f5f5f5" }}>
                                <TableCell sx={{ border: "1px solid #ddd" }}>地段</TableCell>
                                <TableCell sx={{ border: "1px solid #ddd" }}>地號</TableCell>
                                <TableCell sx={{ border: "1px solid #ddd" }}>台灣座標系X座標 (TWD97)</TableCell>
                                <TableCell sx={{ border: "1px solid #ddd" }}>台灣座標系Y座標 (TWD97)</TableCell>
                                <TableCell sx={{ border: "1px solid #ddd" }}>全球座標系經度 (WGS84)</TableCell>
                                <TableCell sx={{ border: "1px solid #ddd" }}>全球座標系緯度 (WGS84)</TableCell>
                            </TableRow>
                        </TableHead>
                        <TableBody>
                            {paginatedRows.length > 0 ? (
                                paginatedRows.map((row) => (
                                    <TableRow
                                        key={row.id}
                                        sx={{
                                            backgroundColor: editedRows[row.id] ? '#fff3e0' : 'transparent'
                                        }}
                                    >
                                        <TableCell sx={{ border: "1px solid #ddd" }}>{row.landName}</TableCell>
                                        <TableCell sx={{ border: "1px solid #ddd" }}>{row.landSerialNumber}</TableCell>
                                        <TableCell sx={{ border: "1px solid #ddd" }}>
                                            <TextField
                                                value={row.x || ""}
                                                size="small"
                                                variant="outlined"
                                                placeholder="請輸入經度 (TWD97)"
                                                type="number"
                                                inputProps={{
                                                    step: "any",
                                                    min: 119,
                                                    max: 122
                                                }}
                                                error={errors[`${row.id}-x`] || false}
                                                helperText={errors[`${row.id}-x`] ? `範圍須介於 ${boundIngBoxex.all.minX} ~ ${boundIngBoxex.all.maxX}` : ""}
                                                onChange={(e) =>
                                                    handleLatLngChange(row.id, "x", e.target.value)
                                                }
                                            />
                                        </TableCell>
                                        <TableCell sx={{ border: "1px solid #ddd" }}>
                                            <TextField
                                                value={row.y || ""}
                                                size="small"
                                                variant="outlined"
                                                placeholder="請輸入緯度 (TWD97)"
                                                type="number"
                                                inputProps={{
                                                    step: "any",
                                                    min: 21,
                                                    max: 26
                                                }}
                                                error={errors[`${row.id}-y`] || false}
                                                helperText={errors[`${row.id}-y`] ? `範圍須介於 ${boundIngBoxex.all.minY} ~ ${boundIngBoxex.all.maxY}` : ""}
                                                onChange={(e) =>
                                                    handleLatLngChange(row.id, "y", e.target.value)
                                                }
                                            />
                                        </TableCell>
                                        <TableCell sx={{ border: "1px solid #ddd" }}>
                                            <TextField
                                                value={row.lat || ""}
                                                size="small"
                                                variant="outlined"
                                                placeholder="系統自動計算"
                                                type="number"
                                                inputProps={{
                                                    step: "any"
                                                }}
                                                disabled
                                            />
                                        </TableCell>
                                        <TableCell sx={{ border: "1px solid #ddd" }}>
                                            <TextField
                                                value={row.long || ""}
                                                size="small"
                                                variant="outlined"
                                                placeholder="系統自動計算"
                                                type="number"
                                                inputProps={{
                                                    step: "any"
                                                }}
                                                disabled
                                            />
                                        </TableCell>
                                    </TableRow>
                                ))
                            ) : (
                                <TableRow>
                                    <TableCell colSpan={4} align="center">
                                        {searchTerm ? "沒有符合搜尋條件的資料" : "沒有資料"}
                                    </TableCell>
                                </TableRow>
                            )}
                        </TableBody>
                    </Table>
                </TableContainer>

                {/* 操作按鈕 */}
                <Box
                    sx={{
                        display: "flex",
                        justifyContent: "flex-end",
                        gap: 2,
                        mt: 2,
                        alignItems: "center",
                    }}
                >
                    <Button
                        variant="outlined"
                        onClick={handleCancel}
                        disabled={Object.keys(editedRows).length === 0 || saving}
                    >
                        取消變更
                    </Button>
                    <Button
                        variant="contained"
                        color="primary"
                        onClick={() => setSaveModalOpen(true)}
                        disabled={Object.keys(editedRows).length === 0 || Object.values(errors).some(Boolean) || saving}
                        startIcon={saving ? <CircularProgress size={20} /> : null}
                    >
                        {saving ? "儲存中..." : "儲存變更"}
                    </Button>
                </Box>

                {/* 分頁 */}
                <Box
                    sx={{
                        display: "flex",
                        justifyContent: "center",
                        mt: 2,
                        alignItems: "center",
                    }}
                >
                    <Pagination
                        count={Math.ceil(filteredRows.length / rowsPerPage)}
                        page={page}
                        onChange={(_, value) => setPage(value)}
                        color="primary"
                    />
                </Box>
            </Box>

            {/* 通知組件 */}
            <Snackbar
                open={notification.open}
                autoHideDuration={6000}
                onClose={handleCloseNotification}
                anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
            >
                <Alert
                    onClose={handleCloseNotification}
                    severity={notification.severity}
                    sx={{ width: '100%' }}
                >
                    {notification.message}
                </Alert>
            </Snackbar>

            <SaveModal
                open={saveModalOpen}
                setOpen={setSaveModalOpen}
                updateLandDataBatch={updateLandDataBatch}
                editedRows={editedRows}
                setSaving={setSaving}
                getTableData={getTableData}
            />
        </div>
    );
}

export default GisPage;
