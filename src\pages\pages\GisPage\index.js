import React, { useEffect, useState } from "react";
import "./GisPage.scss";

import { Api, updateOntoData, readOntoData } from "../../../api/land/Api";
import {
    actualCoordinateConversion,
    autoDetectPKM,
    isValidCoordinate,
    boundIngBoxex
} from "../../pages/EditPage/common/computedColumnValue";
import SaveModal from "./subComponents/SaveModal";

// MUI
import {
    Box,
    TextField,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    Paper,
    Button,
    MenuItem,
    Pagination,
    InputAdornment,
    Select,
    FormControl,
    Typography,
    CircularProgress
} from "@mui/material";
import SearchIcon from "@mui/icons-material/Search";

function StatisticPage() {
    const [rows, setRows] = useState([]);
    const [originalRows, setOriginalRows] = useState([]);
    const [searchTerm, setSearchTerm] = useState("");
    const [editedRows, setEditedRows] = useState({});
    const [rowsPerPage, setRowsPerPage] = useState(10);
    const [page, setPage] = useState(1);
    const [loading, setLoading] = useState(false);
    const [saving, setSaving] = useState(false);
    const [saveModalOpen, setSaveModalOpen] = useState(false);
    const [errors, setErrors] = useState({});

    const validateValue = (field, value) => {
        const num = parseFloat(value);

        if (isNaN(num)) return true;

        if (field === "x") {
            return num < boundIngBoxex.all.minX || num > boundIngBoxex.all.maxX;
        }
        if (field === "y") {
            return num < boundIngBoxex.all.minY || num > boundIngBoxex.all.maxY;
        }
        return false;
    };

    const getTableData = async () => {
        setLoading(true);
        try {
            const res = await readOntoData(Api.getGisTableData());
            const data = res?.data || [];
            setRows(data);
            setOriginalRows(data);
            // 清空編輯狀態
            setEditedRows({});
        } catch (err) {
            console.error("載入資料失敗:", err);
            alert("載入資料失敗，請重新整理頁面");
        } finally {
            setLoading(false);
        }
    }

    useEffect(() => {
        getTableData();
    }, []);

    // 更新經緯度
    const handleLatLngChange = (id, field, value) => {
        // 判斷數值是否符合範圍
        const isError = validateValue(field, value);
        setErrors((prev) => ({
            ...prev,
            [`${id}-${field}`]: isError
        }));


        const currentX = field === "x" ? value : rows.find((row) => row.id === id)?.x;
        const currentY = field === "y" ? value : rows.find((row) => row.id === id)?.y;
        const currentLandName = rows.find((row) => row.id === id)?.landName;
        const currentLandSerialNumber = rows.find((row) => row.id === id)?.landSerialNumber;
        const tempKey = field === 'x' ? 'y' : 'x';

        if (isValidCoordinate(currentX) && isValidCoordinate(currentY)) {
            // 自動判斷是否為澎金馬地區
            const isPKM = autoDetectPKM(currentX, currentY);

            // 進行座標轉換
            const conversionResult = actualCoordinateConversion(
                currentX,
                currentY,
                isPKM
            );

            if (conversionResult) {
                setRows((prevRows) =>
                    prevRows.map((row) =>
                        row.id === id ? { ...row, ...conversionResult } : row
                    )
                );
                setEditedRows((prev) => ({
                    ...prev,
                    [id]: {
                        ...prev[id],
                        ...conversionResult,
                    },
                }));
            }
        }


        setRows((prevRows) =>
            prevRows.map((row) =>
                row.id === id ? { ...row, [field]: value } : row
            )
        );

        setEditedRows((prev) => ({
            ...prev,
            [id]: {
                ...prev[id],
                [field]: value,
                // saveModal需顯示全部資料，除當前修改的欄位外，另外儲存其他欄位資料
                [tempKey]: rows.find((row) => row.id === id)[tempKey],
                landName: currentLandName,
                landSerialNumber: currentLandSerialNumber,
            },
        }));
    };

    // 篩選
    const filteredRows = rows?.filter((row) => {
        const keyword = searchTerm.toLowerCase();
        return (
            row.landName?.toLowerCase().includes(keyword) ||
            row.landSerialNumber?.toLowerCase().includes(keyword) ||
            row?.x?.toString().toLowerCase().includes(keyword) ||
            row?.y?.toString().toLowerCase().includes(keyword) ||
            row?.lat?.toString().toLowerCase().includes(keyword) ||
            row?.long?.toString().toLowerCase().includes(keyword)
        );
    });

    // 分頁後資料
    const paginatedRows = filteredRows.slice(
        (page - 1) * rowsPerPage,
        page * rowsPerPage
    );

    // 批量更新資料的函數
    const updateLandDataBatch = async (data) => {
        const results = [];

        for (const [key, coords] of Object.entries(data)) {
            try {
                const originalRow = originalRows.find((row) => row.id === key);
                if (!originalRow) {
                    throw new Error(`找不到 ID 為 ${key} 的原始資料`);
                }

                const payload = {
                    entrySrc: {
                        graph: "south",
                        classType: "Land",
                        value: {
                            twd97_x: originalRow.x,
                            twd97_y: originalRow.y,
                            wgs84_lat: originalRow.lat,
                            wgs84_long: originalRow.long,

                        },
                        srcId: key
                    },
                    entryDst: {
                        graph: "south",
                        classType: "Land",
                        value: {
                            twd97_x: coords.hasOwnProperty("x")
                                ? coords.x
                                : originalRow.x,
                            twd97_y: coords.hasOwnProperty("y")
                                ? coords.y
                                : originalRow.y,
                            wgs84_lat: coords.hasOwnProperty("lat")
                                ? coords.lat
                                : originalRow.lat,
                            wgs84_long: coords.hasOwnProperty("long")
                                ? coords.long
                                : originalRow.long,
                        },
                        srcId: key
                    }
                };

                const res = await updateOntoData(
                    Api.restfulCRUD(),
                    payload.entrySrc,
                    payload.entryDst,
                    -1,
                    0
                );

                if (!res?.state) {
                    throw new Error(`API 回應狀態異常: ${res?.statusText || '未知錯誤'}`);
                }

                results.push({ id: key, success: true });
            } catch (error) {
                console.error(`更新 ${key} 失敗:`, error);
                results.push({ id: key, success: false, error: error.message });
            }
        }

        return results;
    };

    // 取消變更
    const handleCancel = () => {
        setRows([...originalRows]);
        setEditedRows({});
        setErrors({});
        alert("已取消所有變更");
    };

    if (loading) {
        return (
            <div className="mainBox_shadowMain GisPage" style={{
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                minHeight: '400px'
            }}>
                <CircularProgress />
            </div>
        );
    }

    return (
        <div className="mainBox_shadowMain GisPage">
            {/* 搜尋欄 */}
            <Box className="TopArea" sx={{ mb: 2 }}>
                <TextField
                    placeholder="搜尋地段、地號、經緯度..."
                    variant="outlined"
                    size="small"
                    fullWidth
                    value={searchTerm}
                    onChange={(e) => {
                        setSearchTerm(e.target.value);
                        setPage(1);
                    }}
                    InputProps={{
                        startAdornment: (
                            <InputAdornment position="start">
                                <SearchIcon />
                            </InputAdornment>
                        ),
                    }}
                />
            </Box>

            {/* 表格控制區 */}
            <Box
                sx={{
                    display: "flex",
                    justifyContent: "space-between",
                    mb: 1,
                    alignItems: "center",
                }}
            >
                <Typography variant="body2">
                    共找到 <strong style={{ color: '#1976d2' }}>{filteredRows.length}</strong> 筆資料
                    {Object.keys(editedRows).length > 0 && (
                        <span style={{ color: '#ff9800', marginLeft: '10px' }}>
                            (有 {Object.keys(editedRows).length} 筆未儲存的變更)
                        </span>
                    )}
                </Typography>

                <FormControl size="small" sx={{ minWidth: 120 }}>
                    <Select
                        value={rowsPerPage}
                        onChange={(e) => {
                            setRowsPerPage(e.target.value);
                            setPage(1);
                        }}
                    >
                        <MenuItem value={10}>每頁顯示 10 筆</MenuItem>
                        <MenuItem value={25}>每頁顯示 25 筆</MenuItem>
                        <MenuItem value={50}>每頁顯示 50 筆</MenuItem>
                    </Select>
                </FormControl>
            </Box>

            {/* 表格 */}
            <Box className="BtmArea">
                <TableContainer component={Paper}>
                    <Table sx={{ border: "1px solid #ddd" }}>
                        <TableHead>
                            <TableRow sx={{ backgroundColor: "#f5f5f5" }}>
                                <TableCell sx={{ border: "1px solid #ddd" }}>地段</TableCell>
                                <TableCell sx={{ border: "1px solid #ddd" }}>地號</TableCell>
                                <TableCell sx={{ border: "1px solid #ddd" }}>台灣座標系X座標 (TWD97)</TableCell>
                                <TableCell sx={{ border: "1px solid #ddd" }}>台灣座標系Y座標 (TWD97)</TableCell>
                                <TableCell sx={{ border: "1px solid #ddd" }}>全球座標系經度 (WGS84)</TableCell>
                                <TableCell sx={{ border: "1px solid #ddd" }}>全球座標系緯度 (WGS84)</TableCell>
                            </TableRow>
                        </TableHead>
                        <TableBody>
                            {paginatedRows.length > 0 ? (
                                paginatedRows.map((row) => (
                                    <TableRow
                                        key={row.id}
                                        sx={{
                                            backgroundColor: editedRows[row.id] ? '#fff3e0' : 'transparent'
                                        }}
                                    >
                                        <TableCell sx={{ border: "1px solid #ddd" }}>{row.landName}</TableCell>
                                        <TableCell sx={{ border: "1px solid #ddd" }}>{row.landSerialNumber}</TableCell>
                                        <TableCell sx={{ border: "1px solid #ddd" }}>
                                            <TextField
                                                value={row.x || ""}
                                                size="small"
                                                variant="outlined"
                                                placeholder="請輸入經度 (TWD97)"
                                                type="number"
                                                inputProps={{
                                                    step: "any",
                                                    min: 119,
                                                    max: 122
                                                }}
                                                error={errors[`${row.id}-x`] || false}
                                                helperText={errors[`${row.id}-x`] ? `範圍須介於 ${boundIngBoxex.all.minX} ~ ${boundIngBoxex.all.maxX}` : ""}
                                                onChange={(e) =>
                                                    handleLatLngChange(row.id, "x", e.target.value)
                                                }
                                            />
                                        </TableCell>
                                        <TableCell sx={{ border: "1px solid #ddd" }}>
                                            <TextField
                                                value={row.y || ""}
                                                size="small"
                                                variant="outlined"
                                                placeholder="請輸入緯度 (TWD97)"
                                                type="number"
                                                inputProps={{
                                                    step: "any",
                                                    min: 21,
                                                    max: 26
                                                }}
                                                error={errors[`${row.id}-y`] || false}
                                                helperText={errors[`${row.id}-y`] ? `範圍須介於 ${boundIngBoxex.all.minY} ~ ${boundIngBoxex.all.maxY}` : ""}
                                                onChange={(e) =>
                                                    handleLatLngChange(row.id, "y", e.target.value)
                                                }
                                            />
                                        </TableCell>
                                        <TableCell sx={{ border: "1px solid #ddd" }}>
                                            <TextField
                                                value={row.lat || ""}
                                                size="small"
                                                variant="outlined"
                                                placeholder="系統自動計算"
                                                type="number"
                                                inputProps={{
                                                    step: "any"
                                                }}
                                                disabled
                                            />
                                        </TableCell>
                                        <TableCell sx={{ border: "1px solid #ddd" }}>
                                            <TextField
                                                value={row.long || ""}
                                                size="small"
                                                variant="outlined"
                                                placeholder="系統自動計算"
                                                type="number"
                                                inputProps={{
                                                    step: "any"
                                                }}
                                                disabled
                                            />
                                        </TableCell>
                                    </TableRow>
                                ))
                            ) : (
                                <TableRow>
                                    <TableCell colSpan={4} align="center">
                                        {searchTerm ? "沒有符合搜尋條件的資料" : "沒有資料"}
                                    </TableCell>
                                </TableRow>
                            )}
                        </TableBody>
                    </Table>
                </TableContainer>

                {/* 操作按鈕 */}
                <Box
                    sx={{
                        display: "flex",
                        justifyContent: "flex-end",
                        gap: 2,
                        mt: 2,
                        alignItems: "center",
                    }}
                >
                    <Button
                        variant="outlined"
                        onClick={handleCancel}
                        disabled={Object.keys(editedRows).length === 0 || saving}
                    >
                        取消變更
                    </Button>
                    <Button
                        variant="contained"
                        color="primary"
                        onClick={() => setSaveModalOpen(true)}
                        disabled={Object.keys(editedRows).length === 0 || Object.values(errors).some(Boolean) || saving}
                        startIcon={saving ? <CircularProgress size={20} /> : null}
                    >
                        {saving ? "儲存中..." : "儲存變更"}
                    </Button>
                </Box>

                {/* 分頁 */}
                <Box
                    sx={{
                        display: "flex",
                        justifyContent: "center",
                        mt: 2,
                        alignItems: "center",
                    }}
                >
                    <Pagination
                        count={Math.ceil(filteredRows.length / rowsPerPage)}
                        page={page}
                        onChange={(e, value) => setPage(value)}
                        color="primary"
                    />
                </Box>
            </Box>
            <SaveModal
                open={saveModalOpen}
                setOpen={setSaveModalOpen}
                data={Object.values(editedRows)}
                updateLandDataBatch={updateLandDataBatch}
                editedRows={editedRows}
                setSaving={setSaving}
                getTableData={getTableData}
            />
        </div>
    );
}

export default StatisticPage;
