import React, { useContext, useEffect, useState } from "react";

// material ui
import Box from "@mui/material/Box";
import Grid from "@mui/material/Grid";
import Divider from "@mui/material/Divider";
import Typography from "@mui/material/Typography";

// components
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import DownLoadBtn from "./DownLoadBtn";
import DateTypeSelect from "./DateTypeSelect";

// utils
import { StoreContext } from "../../../../../store/StoreProvider";

function EditorLand() {
  const [state, dispatch] = useContext(StoreContext);
  const { selectDateType } = state.download;
  const [startDate, setStartDate] = useState(new Date());
  const [endDate, setEndDate] = useState(new Date());

  useEffect(() => {
    if (selectDateType !== "multiple") {
      setEndDate(startDate);
    }
  }, [selectDateType, startDate]);

  return (
    <Box className="EditorLand">
      <Grid container p={2} className="topArea">
        <DateTypeSelect />
      </Grid>
      <Divider />
      <Grid container p={2} className="middleArea">
        <Grid item xs={7} className="inputYear">
          <Typography variant="h6">選擇編輯土地資料日期:&nbsp;</Typography>
          <Box className="inputYear_Box">
            <DatePicker
              selected={startDate}
              onChange={(date) => setStartDate(date)}
              dateFormat="yyyy-MM-dd"
            />
            {selectDateType === "multiple" && (
              <Box className="typoBox">
                <Typography> 到 </Typography>
              </Box>
            )}
            {selectDateType === "multiple" && (
              <DatePicker
                selected={endDate}
                onChange={(date) => setEndDate(date)}
                dateFormat="yyyy-MM-dd"
                disabled={selectDateType !== "multiple"}
              />
            )}
          </Box>
        </Grid>
      </Grid>
      <Grid container p={2} className="bottomArea">
        <DownLoadBtn startDate={startDate} endDate={endDate} />
      </Grid>
    </Box>
  );
}

export default EditorLand;
