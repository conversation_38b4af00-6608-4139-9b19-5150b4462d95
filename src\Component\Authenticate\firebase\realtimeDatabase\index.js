// firebase
import {
  getDatabase,
  onValue,
  ref,
  set,
  update,
  child,
  get,
} from "firebase/database";

// commons code
import { isEmpty } from "../../../../utils";

// get user from realtime database
// export const getUser = uid => {
//     const database = getDatabase();
//
//     // make sure uid is correct
//     const safeUid = isEmpty(uid) ? "unknownUid" : uid;
//     return onValue(ref(database,`users/${safeUid}`), (snapshot) => {
//             const data = {};
//             // return result list if it is exist
//             // eslint-disable-next-line no-restricted-syntax
//             if (snapshot.val()) {
//                 for (const [key, value] of Object.entries(snapshot.val())) {
//                     data[key] = value;
//                 }
//             }
//             return data;
//         });
// };

/*
    component: accountManagement
    desc: get all user
    path: [該專案]/users
 */
export const getUsers = () => {
  const dbRef = ref(getDatabase());
  return get(child(dbRef, `users`))
    .then((snapshot) => {
      const data = {};
      // return result list if it is exist
      // eslint-disable-next-line no-restricted-syntax
      if (snapshot.val()) {
        for (const [key, value] of Object.entries(snapshot.val())) {
          data[key] = value;
        }
      }
      return data;
    })
    .catch((error) => {
      console.error(error);
    });
};

/*
    component: accountManagement
    desc: 根據uid找到使用者資訊
    path: [該專案]/users
 */
export const getUser = (uid) => {
  const dbRef = ref(getDatabase());
  const safeUid = isEmpty(uid) ? "unknownUid" : uid;
  return get(child(dbRef, `users/${safeUid}`))
    .then((snapshot) => {
      const data = {};
      // return result list if it is exist
      // eslint-disable-next-line no-restricted-syntax
      if (snapshot.val()) {
        for (const [key, value] of Object.entries(snapshot.val())) {
          data[key] = value;
        }
      }
      return data;
    })
    .catch((error) => {
      console.error(error);
    });
};

// set user to realtime database
export const setUser = (uid, data) => {
  const database = getDatabase();

  // make sure uid is correct
  const safeUid = isEmpty(uid) ? "unknownUid" : uid;
  return set(ref(database, `/users/${safeUid}`), data);
};

// update user to realtime database
export const updateUser = (uid, data) => {
  const database = getDatabase();

  // make sure uid is correct
  const safeUid = isEmpty(uid) ? "unknownUid" : uid;

  const updates = {};
  updates[`users/${safeUid}`] = data;

  return update(ref(database), updates);
};
