import Act from "../actions";

const initState = {
    roles: ["Reader", "Editor", "Admin"],
    autPageLoading: "",
    userInfo: [],
};

const authorityPageReducer = (state = initState, action) => {
    switch (action.type) {
        case Act.SET_AUTPAGELOADING:
            return { ...state, autPageLoading: action.payload };
        case Act.SET_USERINFO:
            return { ...state, userInfo: action.payload };
        default:
            return state;
    }
};

export default authorityPageReducer;