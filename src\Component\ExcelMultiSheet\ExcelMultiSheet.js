import React, { useMemo } from "react";

// excel
import ReactExport from "react-data-export";

const ExcelMultiSheet = ({ filename, excelData }) => {
  const { ExcelFile } = ReactExport;
  const { ExcelSheet, ExcelColumn } = ReactExport.ExcelFile;

  // 表單的排序
  const sheetOrder = Object.keys(excelData).sort(
    (stA, stB) => excelData[stA].order - excelData[stB].order
  );

  const MemoziedExcelFile = useMemo(
    () => (
      <ExcelFile filename={filename}>
        {sheetOrder.map((stName) => {
          let { headers } = excelData[stName];
          let newData = excelData[stName].data;

          // 空表單，幫忙補值。
          if (!newData) {
            newData = [];
            newData[0] = {};
            headers.forEach((h) => {
              newData[0][h.id] = "";
            });
          }

          // headers 排序
          headers = headers.sort((itemA, itemB) => itemA.seq - itemB.seq);

          return (
            <ExcelSheet key={stName} data={newData} name={stName}>
              {headers.map((item) => (
                <ExcelColumn key={item.id} label={item.label} value={item.id} />
              ))}
            </ExcelSheet>
          );
        })}
      </ExcelFile>
    ),
    [filename, excelData]
  );
  return MemoziedExcelFile;
};

export default ExcelMultiSheet;
