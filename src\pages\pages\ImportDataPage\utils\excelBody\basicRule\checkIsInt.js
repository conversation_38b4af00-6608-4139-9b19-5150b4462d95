import { specRow } from "../../../config";

/**
 * 檢查說明: 只能填整數
 * */
const checkIsInt = (cell, worksheet) => {
  const colLabel = worksheet
    .getRow(specRow.labelIdx)
    .getCell(cell.fullAddress.col).value;

  let tmpResStr = "";

  if (cell.value && !Number.isInteger(cell.value)) {
    const reason = "只能填整數";
    tmpResStr += `${cell.address}, [${cell.value}], 欄位:${colLabel}，${reason}。\n`;
  }

  return tmpResStr;
};

export default checkIsInt;
