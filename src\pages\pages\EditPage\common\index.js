import { Api, readOntoData, updateOntoData } from "../../../../api/land/Api";
import { isEmpty } from "../../../../utils";
import { historyType, recordHistory } from "./produceHistory";
import Act from "../../../../store/actions";
import { baseColumnKey, boundIngBoxex } from "./computedColumnValue";
// import isInTaiwanTWD97SimpleBox from "./isInTaiwanTWD97SimpleBox";

// set landName and landSerialNumber
export const setLNAndLSN = (dataArr, dispatch) => {
  const findLN = dataArr.find(
    ({ predicate }) => predicate === "landName"
  )?.value;

  dispatch({
    type: Act.SET_LANDNAME,
    payload: findLN || "",
  });

  const findLSN = dataArr.find(
    ({ predicate }) => predicate === "landSerialNumber"
  )?.value;

  dispatch({
    type: Act.SET_LANDSERIALNUMBER,
    payload: findLSN || "",
  });
};

// 取得最新業主(Owner)資料
export const getOwners = () =>
  new Promise((resolve, reject) => {
    const apiStr = Api.getOwnerList();
    readOntoData(apiStr)
      .then((result) => {
        resolve(result.data);
      })
      .catch((err) => reject(err));
  });

export const checkBasicInfoById = (edit) => {
  const { landId } = edit;

  return new Promise((resolve) => {
    const apiStr = Api.getBasicInfoById(landId);
    readOntoData(apiStr).then((result) => {
      resolve(result.data);
    });
  });
};

export const checkLandMarkInfo = (edit) => {
  const { landId } = edit;

  return new Promise((resolve) => {
    const apiStrLM = Api.getLandMarkID().replace("{landId}", landId);
    readOntoData(apiStrLM).then((result) => {
      if (!isEmpty(result.data)) {
        const LMIds = result.data.map((element) => element.landMarkId);
        const LMApiStr = Api.getLandMarkEvents().replace("{ids}", LMIds.join());
        readOntoData(LMApiStr).then((LMEvents) => {
          resolve(LMEvents.data);
        });
      } else {
        resolve([]);
      }
    });
  });
};

export const checkLandRightsInfo = (edit) => {
  const { landId } = edit;

  return new Promise((resolve) => {
    const apiStrLR = Api.getLandRightsID().replace("{landId}", landId);
    readOntoData(apiStrLR).then(async (result) => {
      if (!isEmpty(result.data)) {
        const LRIds = result.data.map((element) => element.landRightsId);
        const LRApiStr = Api.getLandRightsEvents().replace(
          "{ids}",
          LRIds.join()
        );
        const latestOwnerList = await getOwners();
        readOntoData(LRApiStr).then((LREvents) => {
          let tmpResult = [];
          LREvents.data.forEach((obj) => {
            const findObj = tmpResult.find(
              (element) =>
                element.predicate === obj.predicate &&
                element.landRightsId === obj.landRightsId
            );
            if (!findObj) {
              if (obj.predicate === "hasOwner") {
                const newObj = JSON.parse(JSON.stringify(obj));
                newObj.value = [obj.value];
                tmpResult.push(newObj);
              } else if (obj.predicate === "cause") {
                const newObj = JSON.parse(JSON.stringify(obj));
                newObj.value = obj.value.split("，");
                tmpResult.push(newObj);
              } else {
                tmpResult.push(obj);
              }
            } else if (obj.predicate === "hasOwner") {
              // 如果已經有相同landRightsId與predicate資料存在tmpResult3裡面，value改成用array方式儲存
              findObj.value.push(obj.value);
            }
          });

          // 置換owner ID
          tmpResult = tmpResult.map((obj) => {
            // 替換landRights的Owner ID
            if (obj.predicate === "hasOwner") {
              if (obj.value.length > 0) {
                return {
                  ...obj,
                  value: obj.value.map(
                    (item) =>
                      latestOwnerList.find(
                        (ownerObj) => ownerObj.label === item
                      ).value
                  ),
                };
              }
            }
            return obj;
          });
          // console.log("tmpResult ", tmpResult);
          resolve(tmpResult);
        });
      } else {
        resolve([]);
      }
    });
  });
};

const updateBasicInfo = (preValue, modifyValue) => {
  if (isEmpty(preValue) || isEmpty(modifyValue)) return;

  const { landId } = preValue.find((element) => element.landId);
  const srcValue = preValue.reduce(
    (cur, next) => ({
      ...cur,
      [next.predicate]: next.value,
    }),
    {}
  );

  const dstValue = modifyValue.reduce(
    (cur, next) => ({
      ...cur,
      [next.predicate]: next.value,
    }),
    {}
  );

  const entrySrc = {
    graph: "south",
    classType: "Land",
    srcId: landId,
    value: srcValue,
  };

  const entryDst = {
    graph: "south",
    classType: "Land",
    srcId: landId,
    value: dstValue,
  };
  updateOntoData(Api.restfulCRUD(), entrySrc, entryDst, -1, 0);
};

export const compareBasicInfo = async (edit) => {
  const { basicInfo } = edit;

  const latestBasicInfo = await checkBasicInfoById(edit);
  const preColumn = [];
  const modifyColumn = [];
  basicInfo.forEach((info) => {
    const findObj = latestBasicInfo.find(
      (element) => info.predicate === element.predicate
    );
    if (findObj) {
      if (findObj.value !== info.value) {
        preColumn.push(findObj);
        modifyColumn.push(info);
      }
    } else if (info.value !== "") {
      // 在目前資料庫裏面沒有此欄位資料，表示最新資料為空值，直接增加此property資料
      const tmpPreValue = { ...info };
      tmpPreValue.value = "";
      preColumn.push(tmpPreValue);
      modifyColumn.push(info);
    }
  });

  let tmpRecord = "";
  tmpRecord = recordHistory(preColumn, modifyColumn, historyType.updateBasic);
  if (tmpRecord) {
    updateBasicInfo(preColumn, modifyColumn);
  }

  return tmpRecord;
};

// 取得basicInfo、landMarkInfo、landRightsInfo這三個欄位排序
export const getColOrder = (type) =>
  new Promise((resolve) => {
    if (type === "Basic") {
      const apiStrOrder = Api.getBasicInfoOrder();
      readOntoData(apiStrOrder).then((result) => resolve(result.data));
    } else if (type === "LMK") {
      const apiStrOrder = Api.getLandMarkOrder();
      readOntoData(apiStrOrder).then((result) => resolve(result.data));
    } else if (type === "LRT") {
      const apiStrOrder = Api.getLandRightsOrder();
      readOntoData(apiStrOrder).then((result) => resolve(result.data));
    }
  });

// 比對箱號、作業者資訊
export const compareExtraBasic = async (edit) => {
  const { extraBasic } = edit;

  const latestBasicInfo = await checkBasicInfoById(edit);
  const preCol = [];
  const modifyCol = [];
  extraBasic.forEach((info) => {
    const findObj = latestBasicInfo.find(
      (item) => item.predicate === info.predicate
    );
    if (findObj) {
      if (findObj.value !== info.value) {
        preCol.push(findObj);
        modifyCol.push(info);
      }
    } else if (info.value !== "") {
      // 在目前資料庫裏面沒有此欄位資料，表示最新資料為空值，直接增加此property資料
      const tmpPreValue = { ...info };
      tmpPreValue.value = "";
      preCol.push(tmpPreValue);
      modifyCol.push(info);
    }
  });
  // console.log(preCol, modifyCol);
  let tmpRecord = "";
  tmpRecord = recordHistory(preCol, modifyCol, historyType.updateBasic);
  if (tmpRecord) {
    updateBasicInfo(preCol, modifyCol);
  }

  return tmpRecord;
};

// 解除編輯狀態
export async function cancelEdit(state, displayName, dispatch) {
  const currentBasicInfo = await checkBasicInfoById(state.edit);
  const { landId } = state.edit;

  const findLockUser = currentBasicInfo.find(
    (info) => info.predicate === "lockUser"
  );
  if (findLockUser.value === displayName) {
    const entrySrc = {
      graph: "south",
      classType: "Land",
      srcId: landId,
      value: { lockUser: findLockUser.value },
    };

    const entryDst = {
      graph: "south",
      classType: "Land",
      srcId: landId,
      value: {},
    };
    updateOntoData(Api.restfulCRUD(), entrySrc, entryDst, -1, 0).then(
      (result) => {
        if (result) {
          dispatch({
            type: Act.SET_EDITLOADING,
            payload: false,
          });

          // lockUser清空
          dispatch({
            type: Act.SET_LOCKERUSER,
            payload: "",
          });

          // update landName and landSerialNumber
          setLNAndLSN(currentBasicInfo, dispatch);
        }
      }
    );
    dispatch({
      type: Act.SET_ISLOCK,
      payload: false,
    });
  }
}

// 調整TextField的字體顏色
export const disableFontColor = {
  style: {
    color: "black",
    WebkitTextFillColor: "initial", // input disable也不要影響字體顏色
  },
};

// 取得編輯者資料
export const getLockUser = async (edit) => {
  // 取得編輯者資料
  const latestBasicInfo = await checkBasicInfoById(edit);
  const findLockUser = latestBasicInfo.find(
    (element) => element.predicate === "lockUser"
  );
  if (findLockUser) {
    return findLockUser.value;
  }
  return "";
};

// disable編輯中可以用的功能規則
export const disableEditFunction = (state) => {
  const { isLock, lockUser } = state.edit;
  const { displayName } = state.user;

  return !isLock || (isLock && displayName !== lockUser);
};

// 根據roleArr判斷哪些role需要
export const checkRole = (user, roleArr) => {
  const tmpRoles = user.role.split(",");

  let disabled = true;
  tmpRoles.forEach((tmpRole) => {
    if (roleArr.includes(tmpRole)) {
      disabled = false;
    }
  });

  return disabled;
};

// 取得DropDown column的選項
export const getOptions = (type, property) =>
  new Promise((resolve, reject) => {
    const apiStr = Api.getColDropDownOptions()
      .replace("{class}", type)
      .replace("{property}", property);
    readOntoData(apiStr)
      .then((result) => {
        resolve(result.data);
      })
      .catch((err) => reject(err));
  });

export const autoCompleteColumn = (predicate) => {
  const autoCompleteCol = ["cause", "hasOwner"]; // 會用到AutoComplete的欄位
  return autoCompleteCol.includes(predicate);
};

export const landNameChange = (edit, dispatch) => {
  const { landName } = edit;
  const tmpLandName = landName;
  dispatch({
    type: Act.SET_LANDNAME,
    payload: "",
  });
  dispatch({
    type: Act.SET_LANDNAME,
    payload: tmpLandName,
  });
};

// landMarkInfo 特殊規則條件
const landMarkInfoRule = (state, info, LMIndex) => {
  const { landMarkInfo } = state.edit;

  const checkRule = {
    landRentCheck: [false, false, false, false],
    landAreaCheck: [false, false, false],
    landGradesCheck: [false, false, false], // 則別
    landCategoryCheck: [false, false, false], // 地目
  };

  landMarkInfo.forEach((arr, index) => {
    if (LMIndex === index) {
      const findReason = arr.find((obj) => obj.predicate === "cause").value;
      // 若「登記（變更）原因」為地目變換、地目變換除租、地目變換地租修正、免租期限滿了地目變換，「地目」必與前一筆不同
      const multiReason1 = [
        "地目變換",
        "地目變換除租",
        "地目變換地租修正",
        "免租期限滿了地目變換",
      ];
      if (
        findReason.find((reason) =>
          multiReason1.find((reaStr) => reason.indexOf(reaStr) >= 0)
        )
      ) {
        if (index > 0) {
          const findlandCategory = arr.find(
            (obj) => obj.predicate === "landCategory"
          );
          const findPrelandCategory = landMarkInfo[index - 1].find(
            (obj) => obj.predicate === "landCategory"
          );
          checkRule.landCategoryCheck[0] =
            findPrelandCategory.value === findlandCategory.value;
        }
      }

      // 有原因選擇"地租改正"，"地租"欄位必填
      if (findReason.find((reason) => reason.indexOf("地租改正") >= 0)) {
        const findlandRent = arr.find((obj) => obj.predicate === "landRent");
        // 原因選擇"地租改正"，但卻找不到地租欄位或是地租欄位是空白，跳錯誤題示
        checkRule.landRentCheck[0] = !findlandRent || findlandRent.value === "";

        // 若「登記（變更）原因」為1919地租改正、1935地租改正、1944地租改正，「則別」必與前一筆不同
        if (index > 0) {
          const findValue = arr.find((obj) => obj.predicate === "landGrades");
          const findPreValue = landMarkInfo[index - 1].find(
            (obj) => obj.predicate === "landGrades"
          );
          checkRule.landGradesCheck[0] =
            findPreValue?.value === findValue?.value;
        }
      }

      // 若「登記（變更）原因」為分割出xx、合併xx、測量誤謬訂正、地域變更，「面積（甲）」必與前一筆不同
      const multiReason2 = ["分割出", "合併", "測量誤謬訂正", "地域變更"];
      if (
        findReason.find((reason) =>
          multiReason2.find((reaStr) => reason.indexOf(reaStr) >= 0)
        )
      ) {
        if (index > 0) {
          const findValue = arr.find((obj) => obj.predicate === "landArea");
          const findPreValue = landMarkInfo[index - 1].find(
            (obj) => obj.predicate === "landArea"
          );
          checkRule.landAreaCheck[0] = findPreValue.value === findValue.value;
        }
      }

      // 有原因選擇"分割"，面積欄位填入值，會比上一個landMarkInfo Number地的面積小
      if (findReason.find((reason) => reason.indexOf("分割") >= 0)) {
        const findlandArea = arr.find(
          (obj) => obj.predicate === "landArea"
        ).value;
        const findPrelandArea =
          landMarkInfo[index - 1]?.find((obj) => obj.predicate === "landArea")
            ?.value || 0;
        if (findlandArea && findPrelandArea) {
          // 前一筆土地面積比下一筆土地面積還大，跳錯誤提示
          checkRule.landAreaCheck[1] = findPrelandArea <= findlandArea;
        }
      }

      // 有"免租"、"除租"，地租欄位須填0
      if (
        findReason.find(
          (reason) => reason.indexOf("免租") >= 0 || reason.indexOf("除租") >= 0
        )
      ) {
        const findlandRent = arr.find((obj) => obj.predicate === "landRent");
        if (findlandRent) {
          // 原因選擇"免租"、"除租"，地租欄位卻不是0，跳錯誤題示
          checkRule.landRentCheck[1] = findlandRent.value !== "0";
        }
      }

      // 若「登記（變更）原因」為以上各種原因或新規賦租，「地租（圓）」必與前一筆不同
      const multiReason3 = [...multiReason1, ...multiReason2, "新規賦租"];
      if (
        findReason.find((reason) =>
          multiReason3.find((reaStr) => reason.indexOf(reaStr) >= 0)
        )
      ) {
        if (index > 0) {
          const findValue = arr.find((obj) => obj.predicate === "landRent");
          const findPrValue = landMarkInfo[index - 1].find(
            (obj) => obj.predicate === "landRent"
          );
          checkRule.landRentCheck[2] = findPrValue.value === findValue.value;
        }
      }

      // 原因為"荒地成免租"、"再荒免租"=> "則別"0，"地目"填"荒地”
      if (
        findReason.find((reason) => reason.indexOf("荒地成免租") >= 0) ||
        findReason.find((reason) => reason.indexOf("再荒免租") >= 0)
      ) {
        const findlandGrades = arr.find(
          (obj) => obj.predicate === "landGrades"
        );
        const findlandCategory = arr.find(
          (obj) => obj.predicate === "landCategory"
        );
        // 原因同時選擇"荒地成免租，再荒免租"，"則別"不是0，或是"地目"不是填"荒地”，跳錯誤題示
        if (findlandGrades) {
          checkRule.landGradesCheck[1] = findlandGrades.value !== "0";
        }

        if (findlandCategory) {
          checkRule.landCategoryCheck[1] = findlandCategory.value !== "荒地";
        }
      }

      // 若「登記（變更）原因」為查定、開墾、登錄（戰後），則別、地目、甲數、地租同前一筆。
      const multiReason4 = ["查定", "開墾", "登錄（戰後）"];
      if (
        findReason.find((reason) =>
          multiReason4.find((reaStr) => reason.indexOf(reaStr) >= 0)
        )
      ) {
        if (index > 0) {
          let findValue = arr.find((obj) => obj.predicate === "landRent");
          let findPrValue = landMarkInfo[index - 1].find(
            (obj) => obj.predicate === "landRent"
          );
          checkRule.landRentCheck[3] = findPrValue.value !== findValue.value;

          findValue = arr.find((obj) => obj.predicate === "landArea");
          findPrValue = landMarkInfo[index - 1].find(
            (obj) => obj.predicate === "landArea"
          );
          checkRule.landAreaCheck[2] = findPrValue.value !== findValue.value;

          findValue = arr.find((obj) => obj.predicate === "landGrades");
          findPrValue = landMarkInfo[index - 1].find(
            (obj) => obj.predicate === "landGrades"
          );
          checkRule.landGradesCheck[2] = findPrValue.value !== findValue.value;

          findValue = arr.find((obj) => obj.predicate === "landCategory");
          findPrValue = landMarkInfo[index - 1].find(
            (obj) => obj.predicate === "landCategory"
          );
          checkRule.landCategoryCheck[2] =
            findPrValue.value !== findValue.value;
        }
      }
    }
  });
  return checkRule;
};

/**
 * 檢查基本資料填寫規則
 */
const validationRules = {
  landName: {
    required: true,
    message: "必填欄位",
  },
  landSerialNumber: {
    required: true,
    message: "必填欄位",
    async: true, // 標記為需要異步或外部資料驗證
    customValidator: (value, otherLSNList) => {
      const duplicate = otherLSNList.find(
        (el) => el.landSerialNumber === value
      );
      return {
        isValid: !duplicate,
        message: duplicate
          ? "目前土名在資料庫已經設定此地號，相同土名不能有重複相同地號"
          : "",
      };
    },
  },
  twd97_x: {
    range: [boundIngBoxex.all.minX, boundIngBoxex.all.maxX],
    message: `超過範圍限制應在${boundIngBoxex.all.minX}~${boundIngBoxex.all.maxX}`,
    dependencies: [baseColumnKey.twd97_y],
    dependencyMessage: "X座標和Y座標必須同時填寫",
  },
  twd97_y: {
    range: [boundIngBoxex.all.minY, boundIngBoxex.all.maxY],
    message: `超過範圍限制應在${boundIngBoxex.all.minY}~${boundIngBoxex.all.maxY}`,
    dependencies: [baseColumnKey.twd97_x],
    dependencyMessage: "X座標和Y座標必須同時填寫",
  },
};

export const validateBasicInfoWithRules = (basicInfo, otherLSNList) => {
  const errors = {};

  basicInfo.forEach((item) => {
    const rule = validationRules[item.predicate];
    if (!rule) return;

    // 必填檢查
    if (rule.required && !item.value) {
      errors[item.predicate] = { check: true, helpText: rule.message };
      return;
    }

    // 如果沒有值，跳過後續驗證
    if (!item.value) return;

    // 自定義驗證
    if (rule.customValidator) {
      const result = rule.customValidator(item.value, otherLSNList);
      if (!result.isValid) {
        errors[item.predicate] = { check: true, helpText: result.message };
        return;
      }
    }

    // 範圍檢查
    if (rule.range) {
      const [min, max] = rule.range;
      if (item.value < min || item.value > max) {
        errors[item.predicate] = { check: true, helpText: rule.message };
      }
    }
  });

  // 依賴關係檢查
  basicInfo.forEach((item) => {
    const rule = validationRules[item.predicate];
    if (rule?.dependencies && item.value) {
      rule.dependencies.forEach((depField) => {
        const depItem = basicInfo.find((el) => el.predicate === depField);
        if (!depItem?.value) {
          errors[depField] = {
            check: true,
            helpText: rule.dependencyMessage,
          };
        }
      });
    }
  });

  return errors;
};

export const eachRuleRequiredCheck = (state, info, index) => {
  switch (info.predicate) {
    case "landRightsNumber":
    case "landMarkNumber": {
      let helpText = "";
      let check = false;
      if (info.value.length === 0) {
        helpText = "請填寫登記次序";
        check = true;
      }
      return { helpText, check };
    }
    case "landRent": {
      let helpText = "";
      const tmplandRentCheck = landMarkInfoRule(
        state,
        info,
        index
      ).landRentCheck;
      const checkArr = tmplandRentCheck.filter((str) => str);
      tmplandRentCheck.forEach((check, idx) => {
        if (idx === 0 && check) {
          helpText = '原因選擇"地租改正"，"地租"欄位必填。 ';
        } else if (idx === 1 && check) {
          helpText = '原因有"免租"、"除租"，地租欄位須填0。 ';
        } else if (idx === 2 && check) {
          helpText =
            '若「登記（變更）原因」為"地目變換"、"地目變換除租"、"地目變換地租修正"、"免租期限滿了地目變換"、' +
            '"地租改正"、"分割出xx"、"合併xx"、"測量誤謬訂正"、"地域變更"或"新規賦租"，「地租（圓）」必與前一筆不同';
        } else if (idx === 3 && check) {
          helpText =
            "若「登記（變更）原因」為查定、開墾、登錄（戰後），則別、地目、甲數、地租同前一筆。";
        }
      });
      return { helpText, check: checkArr.length >= 1 };
    }
    case "landArea": {
      let helpText = "";
      const tmplandAreaCheck = landMarkInfoRule(
        state,
        info,
        index
      ).landAreaCheck;
      tmplandAreaCheck.forEach((check, idx) => {
        if (idx === 0 && check) {
          helpText =
            "若「登記（變更）原因」為分割出xx、合併xx、測量誤謬訂正、地域變更，「面積（甲）」必與前一筆不同";
        } else if (idx === 1 && check) {
          helpText =
            '原因選擇"分割"，面積欄位填入值，會比上一筆土地標示變更次序的面積小';
        } else if (idx === 2 && check) {
          helpText =
            "若「登記（變更）原因」為查定、開墾、登錄（戰後），則別、地目、甲數、地租同前一筆。";
        }
      });
      const checkArr = tmplandAreaCheck.filter((str) => str);
      return { helpText, check: checkArr.length >= 1 };
    }
    case "landGrades": {
      let helpText = "";
      const tmplandGradesCheck = landMarkInfoRule(
        state,
        info,
        index
      ).landGradesCheck;
      tmplandGradesCheck.forEach((check, idx) => {
        if (idx === 0 && check) {
          helpText =
            '若「登記（變更）原因」為"地租改正"，「則別」必與前一筆不同';
        } else if (idx === 1 && check) {
          helpText = '原因為"荒地成免租"、"再荒免租"=> "則別"0';
        } else if (idx === 2 && check) {
          helpText =
            "若「登記（變更）原因」為查定、開墾、登錄（戰後），則別、地目、甲數、地租同前一筆。";
        }
      });
      const checkArr = tmplandGradesCheck.filter((str) => str);
      return { helpText, check: checkArr.length >= 1 };
    }
    case "landCategory": {
      let helpText = "";
      const tmplandCategoryCheck = landMarkInfoRule(
        state,
        info,
        index
      ).landCategoryCheck;
      tmplandCategoryCheck.forEach((check, idx) => {
        if (idx === 0 && check) {
          helpText =
            '若「登記（變更）原因」為"地目變換"、"地目變換除租"、"地目變換地租修正"、"免租期限滿了地目變換"，「地目」必與前一筆不同';
        } else if (idx === 1 && check) {
          helpText = '原因為"荒地成免租"、"再荒免租"=> "地目"填"荒地"';
        } else if (idx === 2 && check) {
          helpText =
            "若「登記（變更）原因」為查定、開墾、登錄（戰後），則別、地目、甲數、地租同前一筆。";
        }
      });
      const checkArr = tmplandCategoryCheck.filter((str) => str);
      return { helpText, check: checkArr.length >= 1 };
    }
    default:
      return false;
  }
};

// 確認相同年分
export const checkAllStartDate = (
  tmpLandInfo,
  item,
  checkDateRepeat,
  landInfoType
) => {
  const tmpCheckDateRepeat = JSON.parse(JSON.stringify(checkDateRepeat));
  const tmpInfo = JSON.parse(JSON.stringify(tmpLandInfo));
  const findStartDate = Object.values(tmpInfo[item.index]).find(
    (element) => element.predicate === "hasStartDate"
  );
  if (findStartDate) {
    // 跟其他的變更次序hasStartDate比對
    tmpInfo.forEach((infoObj, infoIndex) => {
      if (infoIndex !== item.index) {
        const findObj = infoObj.find(
          (element) => element.predicate === "hasStartDate"
        );
        if (findObj) {
          if (findObj.value === findStartDate.value && findObj.value) {
            tmpCheckDateRepeat[item.index] = true;
          }
        }
      } else {
        // 比對其他的hasStartDate value，如果都不相同，不顯示錯誤提示
        const tmpArr = tmpInfo.filter((arr) =>
          arr.find((element) => element[landInfoType] !== item[landInfoType])
        );
        const findSameDate = tmpArr.find(
          (arr) =>
            arr.find((element) => element.predicate === "hasStartDate")
              .value === findStartDate.value
        );
        if (!findSameDate) {
          tmpCheckDateRepeat[item.index] = false;
        }
      }
    });
  }
  return tmpCheckDateRepeat;
};

// 判斷所有資料是否符合特殊規則
export const checkAllRule = (state, otherLSNList) => {
  const { basicInfo, landRightInfo, landMarkInfo } = state.edit;

  const checkAll = {
    landMarkDateRule: false,
    landRightsDateRule: false,
    landMarkRule: false,
    basicInfoRule: false,
  };

  let tmpCheckDateRepeat = landMarkInfo.map(() => false);
  landMarkInfo.forEach((infoArr, index) => {
    // 確認landMarkInfo不能有重複時間規則
    const findStartDate = infoArr.find(
      (element) => element.predicate === "hasStartDate"
    );
    findStartDate.index = index;
    if (findStartDate) {
      tmpCheckDateRepeat = checkAllStartDate(
        landMarkInfo,
        findStartDate,
        tmpCheckDateRepeat
      );
      if (tmpCheckDateRepeat.find((rule) => rule)) {
        checkAll.landMarkDateRule = true;
      }
    }

    // 確認landMarkInfo各特殊欄位規則
    infoArr.forEach((info) => {
      const tmpResult = landMarkInfoRule(state, info, index);
      Object.values(tmpResult).forEach((value) => {
        if (Array.isArray(value)) {
          const tmpValue = value.find((str) => str);
          if (tmpValue) {
            checkAll.landMarkRule = true;
          }
        } else if (typeof value === "boolean") {
          if (value) {
            checkAll.landMarkRule = true;
          }
        }
      });
    });
  });

  tmpCheckDateRepeat = landRightInfo.map(() => false);
  landRightInfo.forEach((infoArr, index) => {
    // 確認landRights不能有重複時間規則
    const findStartDate = infoArr.find(
      (element) => element.predicate === "hasStartDate"
    );
    findStartDate.index = index;
    if (findStartDate) {
      tmpCheckDateRepeat = checkAllStartDate(
        landRightInfo,
        findStartDate,
        tmpCheckDateRepeat
      );
      if (tmpCheckDateRepeat.find((rule) => rule)) {
        checkAll.landRightsDateRule = true;
      }
    }
  });

  // basicInfoRule
  const tmpCheck = validateBasicInfoWithRules(basicInfo, otherLSNList);
  if (Object.keys(tmpCheck).length > 0) {
    checkAll.basicInfoRule = true;
  }

  return Object.values(checkAll).some((check) => check);
};
