@use "../../../scss/common";

.LandData {
  padding-top: 2%;
  .topArea {
    display: flex;
    flex-direction: column;
  }

  .InputColumn{
    //border: 1px red solid;
    display: flex;
    justify-content: center;
    //align-items: center;
  }
  .DownLoadBtn {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    .DownLoadBtnBox {
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      padding: 10px;
      .DWButton {
        margin: 10px;
      }
    }
  }
}

.EditorLand {
  //border: 1px red solid;
  padding-top: 2%;
  .middleArea {
    .inputYear {
      display: flex;
      flex-direction: column;
      .typoBox {
        text-align: center;
        width: 10%;
      }
      &_Box {
        margin-top: 0.5rem;
        display: flex;
        align-items: center;
      }
      .react-datepicker-wrapper {
        width: auto;
      }
      .react-datepicker__input-container input {
        //display: block;
        border-radius: 8px;
      }
      .react-datepicker-popper {
        .react-datepicker {
          .react-datepicker__triangle {
            transform: initial!important;
            left: 20px!important;
          }
        }
      }
    }
  }
  .bottomArea {
    justify-content: flex-end;
  }
}
