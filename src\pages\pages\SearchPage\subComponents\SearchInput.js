import React, { useContext, useEffect } from "react";

import SearchIcon from "@mui/icons-material/Search";
import { Box, TextField, InputAdornment } from "@mui/material";
import { StoreContext } from "../../../../store/StoreProvider";
import CustomDebounce from "../../../../hooks/CustomDebounce";
import { isEmpty } from "../../../../utils";
import { Api, readOntoData, fetchPOSTData } from "../../../../api/land/Api";
import Act from "../../../../store/actions";

function SearchInput() {
  const [state, dispatch] = useContext(StoreContext);
  const { searchColList, keyWord } = state.search;
  const debounceValue = CustomDebounce(keyWord, 200);

  useEffect(() => {
    const searchColStr = searchColList
      .filter((col) => col.select)
      .map((col) => col.value)
      .join();

    if (
      isEmpty(debounceValue) ||
      isEmpty(searchColList) ||
      isEmpty(searchColStr)
    ) {
      dispatch({
        type: Act.SET_RESULTLIST,
        payload: [],
      });
      return;
    }

    const apiStr = Api.getPostSearchInfo();
    dispatch({
      type: Act.SET_SEARCHLOADING,
      payload: true,
    });
    fetchPOSTData({
      apiStr,
      entry: {
        ids: "",
        keyword: debounceValue,
      },
    }).then((result) => {
      dispatch({
        type: Act.SET_RESULTLIST,
        payload: result.data,
      });
      dispatch({
        type: Act.SET_SEARCHDURATION,
        payload: result.durationSS,
      });
      dispatch({
        type: Act.SET_SEARCHLOADING,
        payload: false,
      });
    });

    // const apiStr = Api.getSearchInfo()
    //   .replace("{ids}", searchColStr)
    //   .replace("{keyword}", debounceValue);
    // dispatch({
    //   type: Act.SET_SEARCHLOADING,
    //   payload: true,
    // });
    // readOntoData(apiStr).then((result) => {
    //   dispatch({
    //     type: Act.SET_RESULTLIST,
    //     payload: result.data,
    //   });
    //   dispatch({
    //     type: Act.SET_SEARCHDURATION,
    //     payload: result.durationSS,
    //   });
    //   dispatch({
    //     type: Act.SET_SEARCHLOADING,
    //     payload: false,
    //   });
    // });
  }, [debounceValue, searchColList]);

  return (
    <Box className="SearchInput">
      <TextField
        fullWidth
        label="搜尋"
        InputProps={{
          endAdornment: (
            <InputAdornment position="start">
              <SearchIcon />
            </InputAdornment>
          ),
        }}
        variant="outlined"
        value={keyWord}
        onChange={(event) => {
          dispatch({
            type: Act.SET_KEYWORD,
            payload: event.target.value,
          });
        }}
      />
    </Box>
  );
}

export default SearchInput;
