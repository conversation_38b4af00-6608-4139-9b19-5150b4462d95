import checkIsInt from "../basicRule/checkIsInt";
import { specRow } from "../../../config";
import evtType from "../../../../../../utils/evtType";

const checkDateEvt = (cell, worksheet, redStartDate) => {
  let tmpStr = "";

  const colLabel = worksheet
    .getRow(specRow.labelIdx)
    .getCell(cell.fullAddress.col).value;

  // 確認是整數
  tmpStr = checkIsInt(cell, worksheet);
  if (!tmpStr && cell.value) {
    // 判斷同一筆土地，在標示變更或權力變更事件中有無重複日期
    const idSplitSymbol = "-->";
    const landEvtType = worksheet
      .getRow(specRow.idIdx)
      .getCell(cell.fullAddress.col)
      .value.split(idSplitSymbol)[2];

    if (landEvtType === evtType.LandMark) {
      if (!redStartDate.lm.includes(cell.value)) {
        redStartDate.lm.push(cell.value);
      } else {
        const reason = "同土地的土地標示登記（變更）時間中，不能存在相同年分";
        tmpStr += `${cell.address}, [${cell.value}], 欄位:${colLabel}，${reason}。\n`;
      }
    } else if (landEvtType === evtType.LandRights) {
      if (!redStartDate.lr.includes(cell.value)) {
        redStartDate.lr.push(cell.value);
      } else {
        const reason = "同土地的土地權力登記（變更）時間中，不能存在相同年分";
        tmpStr += `${cell.address}, [${cell.value}], 欄位:${colLabel}，${reason}。\n`;
      }
    }
  }

  return tmpStr;
};

export default checkDateEvt;
