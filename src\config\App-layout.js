// HTML Header: Nav Bar
import HomePage from "../pages/pages/HomePage";
import SearchPage from "../pages/pages/SearchPage";
import EditPage from "../pages/pages/EditPage";
import DownloadPage from "../pages/pages/DownloadPage";
import AuthorityPage from "../pages/pages/AuthorityPage";
import SignIn from "../Component/auth/SignIn";
import SignOut from "../Component/auth/SignOut";
import authority from "./App-authority";
import pathConfig from "./route-path";
import ImportDataPage from "../pages/pages/ImportDataPage";
import HistoryPage from "../pages/pages/HistoryPage";
import StatisticPage from "../pages/pages/StatisticPage";
import GisPage from "../pages/pages/GisPage";

const menus = {
  menuLeft: [
    {
      id: "menu-left-01",
      name: pathConfig.root.label,
      path: pathConfig.root.url,
      authority: authority.Home,
    },
    {
      id: "menu-left-08",
      name: pathConfig.statistics.label,
      path: pathConfig.statistics.url,
      authority: authority.Statistics,
    },
    {
      id: "menu-left-02",
      name: pathConfig.edit.label,
      path: pathConfig.edit.url,
      authority: authority.Edit,
    },
    {
      id: "menu-left-09",
      name: pathConfig.gis.label,
      path: pathConfig.gis.url,
      authority: authority.Gis,
    },
    {
      id: "menu-left-03",
      name: pathConfig.search.label,
      path: pathConfig.search.url,
      authority: authority.Search,
    },
    {
      id: "menu-left-04",
      name: pathConfig.download.label,
      path: pathConfig.download.url,
      authority: authority.Download,
    },
    {
      id: "menu-left-05",
      name: pathConfig.auth.label,
      path: pathConfig.auth.url,
      authority: authority.Authority,
    },
    {
      id: "menu-left-06",
      name: pathConfig.importData.label,
      path: pathConfig.importData.url,
      authority: authority.ImportData,
    },
    {
      id: "menu-left-07",
      name: pathConfig.history.label,
      path: pathConfig.history.url,
      authority: authority.History,
    },
  ],
  menuRight: [
    // showOnLogin: user login 時，是否要顯示
    {
      id: "menu-right-01",
      name: pathConfig.signIn.label,
      path: pathConfig.signIn.url,
      authority: authority.SignIn,
    },
    {
      id: "menu-right-02",
      name: pathConfig.signOut.label,
      path: pathConfig.signOut.url,
      authority: authority.SignOut,
      showOnLogin: true,
    },
  ],
};

// HTML Body: URL Redirect
const routes = [
  {
    id: "route-01",
    path: pathConfig.root.url,
    public: true,
    component: HomePage,
    authority: authority.Home,
  },
  {
    id: "route-02",
    path: pathConfig.edit.url,
    public: true,
    component: EditPage,
    authority: authority.Edit,
  },
  {
    id: "route-03",
    path: pathConfig.search.url,
    public: true,
    component: SearchPage,
    authority: authority.Search,
  },
  {
    id: "route-04",
    path: pathConfig.download.url,
    public: true,
    component: DownloadPage,
    authority: authority.Download,
  },
  {
    id: "route-05",
    path: pathConfig.auth.url,
    public: true,
    component: AuthorityPage,
    authority: authority.Authority,
  },
  {
    id: "route-06",
    path: pathConfig.signIn.url,
    public: true,
    component: SignIn,
    authority: authority.SignIn,
  },
  {
    id: "route-07",
    path: pathConfig.signOut.url,
    public: true,
    component: SignOut,
    authority: authority.SignOut,
  },
  {
    id: "route-08",
    path: pathConfig.importData.url,
    public: process.env.REACT_APP_IMPORT === "true",
    component: ImportDataPage,
    authority: authority.ImportData,
  },
  {
    id: "route-09",
    path: pathConfig.history.url,
    public: true,
    component: HistoryPage,
    authority: authority.History,
  },
  {
    id: "route-10",
    path: pathConfig.statistics.url,
    public: true,
    component: StatisticPage,
    authority: authority.Statistics,
  },
  {
    id: "route-11",
    path: pathConfig.gis.url,
    public: true,
    component: GisPage,
    authority: authority.Gis,
  },
];

// 當使用者不登入, 開放的頁面
const routesPublicNotLogin = [pathConfig.root.url, pathConfig.signIn.url];

/**
 * 使用者是否可看見 menu 或進入該頁面
 * @param item {Object}
 * @param role {String || Array} : "admin, approver" or ['admin', 'approver']
 * @returns {boolean}
 */
const allowGetIn = (item, role) => {
  if (!(item && role)) {
    return routesPublicNotLogin.indexOf(item.path) >= 0;
  }
  if (!(Array.isArray(role) || typeof role === "string")) return false;
  // 把 string role 轉成 array
  let roles = role;
  if (typeof role === "string") {
    roles = role.split(",");
  }
  let getIn = false;
  roles.forEach((r) => {
    if (item.authority.indexOf(r.toLowerCase()) >= 0) getIn = true;
  });
  return getIn;
};

export { menus, routes, allowGetIn };
