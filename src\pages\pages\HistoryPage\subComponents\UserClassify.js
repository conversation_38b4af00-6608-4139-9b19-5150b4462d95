import React, { useEffect, useState } from "react";

// material ui
import ListItemButton from "@mui/material/ListItemButton";
import ListItemIcon from "@mui/material/ListItemIcon";
import PersonIcon from "@mui/icons-material/Person";
import ListItemText from "@mui/material/ListItemText";
import ExpandLess from "@mui/icons-material/ExpandLess";
import ExpandMore from "@mui/icons-material/ExpandMore";
import Collapse from "@mui/material/Collapse";
import List from "@mui/material/List";
import ListItem from "@mui/material/ListItem";
import WorkHistoryIcon from "@mui/icons-material/WorkHistory";

// api
import Divider from "@mui/material/Divider";
import { Api, readOntoData } from "../../../../api/land/Api";

function UserClassify() {
  const [sortedList, setSortedList] = useState([]);

  useEffect(() => {
    const apiStr = Api.getHistoryUserList();
    readOntoData(apiStr).then((res) => {
      const sortByDate = res.data.map((el) => ({
        ...el,
        open: false,
        infoData: [],
      }));
      setSortedList(sortByDate);
    });
  }, []);

  const handleClick = async (evt) => {
    const tmpList = JSON.parse(JSON.stringify(sortedList));
    const findObj = tmpList.find((el) => el.user === evt.target.innerText);
    if (findObj) {
      findObj.open = !findObj.open;

      if (findObj.open) {
        const apiStr = Api.getHistoryUserListInfo().replace(
          "{userName}",
          findObj.user
        );
        const tmpRes = await readOntoData(apiStr);
        findObj.infoData = tmpRes.data;
      } else {
        findObj.infoData = [];
      }
      setSortedList(tmpList);
    }
  };

  return (
    <List component="nav">
      {sortedList.map((el) => {
        const { user, open, infoData } = el;
        return (
          <React.Fragment key={el.user}>
            <ListItemButton onClick={handleClick}>
              <ListItemIcon>
                <PersonIcon />
              </ListItemIcon>
              <ListItemText primary={user} />
              {open ? <ExpandLess /> : <ExpandMore />}
            </ListItemButton>
            <Collapse in={open}>
              {infoData.map((info) => (
                <List component="div" key={info.hisId}>
                  <ListItem
                    sx={{ pl: 4 }}
                    style={{
                      display: "grid",
                      gridTemplateColumns: "50px 10% 10% 1fr",
                    }}
                  >
                    <ListItemIcon>
                      <WorkHistoryIcon />
                    </ListItemIcon>
                    <ListItemText primary={info.time} sx={{ mr: 2 }} />
                    <ListItemText primary={info.landStr} />
                    <ListItemText primary={info.action} />
                  </ListItem>
                  <Divider variant="inset" component="li" />
                </List>
              ))}
            </Collapse>
          </React.Fragment>
        );
      })}
    </List>
  );
}

export default UserClassify;
