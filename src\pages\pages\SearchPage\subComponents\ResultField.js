import React, { useContext, useEffect, useState } from "react";
import { Link } from "react-router-dom";

// material ui
import Grid from "@mui/material/Grid";
import List from "@mui/material/List";
import ListItem from "@mui/material/ListItem";
import ListItemText from "@mui/material/ListItemText";
import TableContainer from "@mui/material/TableContainer";
import Table from "@mui/material/Table";
import TableRow from "@mui/material/TableRow";
import TableCell from "@mui/material/TableCell";
import TableBody from "@mui/material/TableBody";
import Typography from "@mui/material/Typography";

// utils
import { StoreContext } from "../../../../store/StoreProvider";
import { isEmpty } from "../../../../utils";
import { Api, fetchPOSTData } from "../../../../api/land/Api";
import { returnHref, sortingNumberWithBar } from "../common";
import { canAccessEdit } from "../../../../utils/permissions";

// component
import CustomLoading from "../../../../Component/CustomLoading/CustomLoading";
import CustomPagination from "../../../../Component/CustomPagination/CustomPagination";
import NoResult from "./NoResult";
import Act from "../../../../store/actions";

function ResultField() {
  const [state, dispatch] = useContext(StoreContext);
  const { resultList, searchLoading, pageNumber, keyWord, searchDuration } =
    state.search;
  const { user } = state;
  const { role } = user;
  const canEdit = canAccessEdit(role);
  const pageShowCount = 5;
  const [allData, setAllData] = useState([]);

  useEffect(() => {
    if (isEmpty(keyWord)) {
      setAllData([]);
      dispatch({
        type: Act.SET_PAGENUMBER,
        payload: 1,
      });
    }
  }, [keyWord]);

  useEffect(() => {
    if (isEmpty(resultList)) return;
    const tmpList = resultList.map((element) => element.cardId);
    const apiStr = Api.getPostLandData();
    fetchPOSTData({
      apiStr,
      entry: {
        ids: tmpList.join(),
      },
    }).then((result) => {
      // 根據resultList的土地編號順序取出資料顯示
      const mainCol = ["landName", "landSerialNumber"];
      const tmpResult = tmpList.map((id) => {
        let tmpData = result.data.filter((element) => element.landId === id);
        tmpData = tmpData.reduce((cur, next) => {
          if (mainCol.includes(next.predicate)) {
            return { ...cur, [next.predicate]: next.value };
          }
          if (cur.data) {
            return {
              ...cur,
              data: [...cur.data, { value: next.value, object: next.object }],
            };
          }
          return {
            ...cur,
            data: [{ value: next.value, object: next.object }],
          };
        }, {});
        return { ...tmpData, landId: id };
      });

      // 根據landName整理一起
      const allLandName = tmpResult
        .map((element) => element.landName)
        .filter(
          (element, pos) =>
            tmpResult.findIndex((item) => item.landName === element) === pos
        );

      let tmpAllData = [];
      allLandName.forEach((LMName) => {
        let tmpArr = tmpResult.filter((element) => element.landName === LMName);
        // 依照landSerialNumber排列
        tmpArr = tmpArr.sort((cur, next) => sortingNumberWithBar(cur, next));
        tmpAllData = [...tmpAllData, ...tmpArr];
      });
      setAllData(tmpAllData);
    });
  }, [resultList]);

  return (
    <Grid item xs={11}>
      {!isEmpty(keyWord) && !searchLoading && (
        <Typography>
          總共 {resultList.length} 筆資料，{searchDuration} 秒
        </Typography>
      )}
      <TableContainer style={{ height: "100%" }}>
        {!isEmpty(resultList) || !searchLoading ? (
          <Table>
            <TableBody>
              {allData
                .filter(
                  (element, index) =>
                    index >= pageShowCount * (pageNumber - 1) &&
                    index < pageShowCount * pageNumber
                )
                .map((element, index) => (
                  <TableRow key={index}>
                    <TableCell width="50%" style={{ textAlign: "center" }}>
                      {canEdit ? (
                        <Link
                          to={returnHref(element)}
                          target="_blank"
                          style={{ textDecoration: "none", color: "blue" }}
                        >
                          <h2>{`[${element.landName}] - [${element.landSerialNumber}]`}</h2>
                        </Link>
                      ) : (
                        <h2 style={{ color: "#666", cursor: "not-allowed" }}>
                          {`[${element.landName}] - [${element.landSerialNumber}]`}
                        </h2>
                      )}
                    </TableCell>
                    <TableCell>
                      {element.data.map((item, dataIndex) => (
                        <List key={dataIndex} style={{ padding: "0" }}>
                          <ListItem disablePadding>
                            <h3
                              style={{ margin: "0" }}
                            >{`${item.object}：`}</h3>
                            <ListItemText primary={item.value} />
                          </ListItem>
                        </List>
                      ))}
                    </TableCell>
                  </TableRow>
                ))}
            </TableBody>
          </Table>
        ) : (
          <CustomLoading />
        )}
        {!isEmpty(keyWord) && isEmpty(resultList) && !searchLoading && (
          <NoResult />
        )}
      </TableContainer>
      {resultList.length > 5 && (
        <CustomPagination pageShowCount={pageShowCount} />
      )}
    </Grid>
  );
}

export default ResultField;
