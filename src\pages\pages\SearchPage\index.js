import React, { useContext, useEffect } from "react";

// material-ui
import { Box, Grid } from "@mui/material";

// css
import "./SearchPage.scss";

//
import SearchInput from "./subComponents/SearchInput";
import ClassifyItem from "./subComponents/ClassifyItem";
import ClearAll from "./subComponents/ClearAll";
import ChipItems from "./subComponents/ChipItems";
import ResultField from "./subComponents/ResultField";
import { StoreContext } from "../../../store/StoreProvider";
import { Api, readOntoData } from "../../../api/land/Api";
import Act from "../../../store/actions";

function SearchPage() {
  const [_, dispatch] = useContext(StoreContext);

  useEffect(() => {
    const apiStr = Api.getSearchColNames();
    readOntoData(apiStr).then((result) => {
      const tmpSearchColList = [];
      result.data.forEach((element) => {
        tmpSearchColList.push({
          label: element.field,
          value: element.predicate,
          select: true,
        });
      });

      dispatch({
        type: Act.SET_SEARCHCOLLIST,
        payload: tmpSearchColList,
      });
    });
  }, []);

  return (
    <div className="mainBox_shadowMain">
      <Box className="searchArea">
        <Grid
          container
          spacing={2}
          justifyContent="center"
          className="inputCol"
        >
          <Grid item xs={6}>
            <SearchInput />
          </Grid>
          <Grid item xs={3}>
            <ClassifyItem />
          </Grid>
          <Grid item xs={2}>
            <ClearAll />
          </Grid>
        </Grid>
        <Grid container justifyContent="center" className="labelCol">
          <ChipItems />
        </Grid>
      </Box>
      <Box className="resultField">
        <Grid container justifyContent="center" style={{ height: "100%" }}>
          <ResultField />
        </Grid>
      </Box>
    </div>
  );
}

export default SearchPage;
