import React, { useContext, useEffect, useState } from "react";

import "./AuthorityPage.scss";
import { Box, Tabs, Tab } from "@mui/material";
import ValidUser from "./subComponents/ValidUser";
import InvalidUser from "./subComponents/InvalidUser";
import { StoreContext } from "../../../store/StoreProvider";
import Act from "../../../store/actions";
import { getUsers } from "../../../Component/Authenticate/firebase/realtimeDatabase";
import { isEmpty } from "../../../utils";
import NoUser from "./subComponents/NoUser";

function AuthorityPage() {
  const [value, setValue] = useState(0);

  const [state, dispatch] = useContext(StoreContext);
  const { userInfo, roles } = state.authority;
  const [validUser, setValidUser] = useState([]);
  const [invalidUser, setInvalidUser] = useState([]);

  useEffect(() => {
    dispatch({
      type: Act.SET_AUTPAGELOADING,
      payload: true,
    });
    getUsers().then((result) => {
      const tmpUserInfo = [];
      Object.keys(result).forEach((id) => {
        tmpUserInfo.push(result[id]);
      });
      dispatch({
        type: Act.SET_AUTPAGELOADING,
        payload: false,
      });
      dispatch({
        type: Act.SET_USERINFO,
        payload: tmpUserInfo,
      });
    });
  }, []);

  useEffect(() => {
    const tmpArr = userInfo.filter((info) => {
      let exist = false;
      roles.forEach((role) => {
        if (info.role.indexOf(role.toLowerCase()) >= 0) {
          exist = true;
        }
      });
      return exist;
    });
    setValidUser(tmpArr);

    const tmpArr2 = userInfo.filter((info) => {
      let exist = true;
      roles.forEach((role) => {
        if (info.role.indexOf(role.toLowerCase()) >= 0) {
          exist = false;
        }
      });
      return exist;
    });
    setInvalidUser(tmpArr2);
  }, [userInfo]);

  return (
    <Box className="mainBox_shadowMain">
      <Box sx={{ borderBottom: 1, borderColor: "divider" }}>
        <Tabs
          value={value}
          onChange={(event, newValue) => setValue(newValue)}
          variant="fullWidth"
        >
          <Tab label="Valid" />
          <Tab label="Invalid" />
        </Tabs>
      </Box>
      {value === 0 && !isEmpty(validUser) && <ValidUser />}
      {value === 0 && isEmpty(validUser) && <NoUser name="使用者" />}
      {value === 1 && !isEmpty(invalidUser) && <InvalidUser />}
      {value === 1 && isEmpty(invalidUser) && <NoUser name="申請人" />}
    </Box>
  );
}

export default AuthorityPage;
