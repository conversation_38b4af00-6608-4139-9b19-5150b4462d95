import React, { useContext } from "react";

// material ui
import Autocomplete from "@mui/material/Autocomplete";
import TextField from "@mui/material/TextField";

// utils
import { StoreContext } from "../../../../../store/StoreProvider";
import Act from "../../../../../store/actions";

function LandTypeSelect() {
  const [state, dispatch] = useContext(StoreContext);
  const { landType } = state.download;

  return (
    <Autocomplete
      options={landType.map((element) => element.label)}
      renderInput={(params) => (
        <TextField {...params} label="選擇土地資料類別" />
      )}
      onChange={(event, value) => {
        let tmpLandType = JSON.parse(JSON.stringify(landType));
        // init landType
        tmpLandType = tmpLandType.map((element) => ({
          ...element,
          select: false,
        }));

        const findObj = tmpLandType.find((element) => element.label === value);
        if (findObj) {
          findObj.select = true;
        }

        dispatch({
          type: Act.SET_LANDTYPE,
          payload: tmpLandType,
        });
      }}
    />
  );
}

export default LandTypeSelect;
