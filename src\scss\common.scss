@use "sass:map";

*{
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  font-family: "Noto Sans TC", sans-serif;
}

@mixin center {
  display: flex;
  justify-content: center;
  align-items: center;
}

$breakpoints: (
    "xs": 0px,
    "sm": 576px,
    "md": 768px,
    "lg": 992px,
    "xl": 1280px
);

.mainBox {
  &_shadowMain {
    border: 1px black solid;
    border-radius: 5px;
    box-shadow: 5px 5px #A6A6A6;
    min-height: 80vh;
    //height: fit-content;
    padding: 1%;
  }
}

@media only screen and (max-width: map.get($breakpoints, "md")) {
  //.mainBox {
  //  padding: 2.5rem 1.5rem;
  //}
}
