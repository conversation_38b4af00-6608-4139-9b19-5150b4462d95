{"name": "land", "version": "0.2.0", "private": true, "dependencies": {"@emotion/react": "^11.7.1", "@emotion/styled": "^11.6.0", "@mui/icons-material": "^5.3.1", "@mui/material": "^5.4.0", "axios": "^0.25.0", "base64url": "^3.0.1", "d3": "^7.8.5", "dom-to-image": "^2.6.0", "exceljs": "^4.4.0", "file-saver": "^2.0.5", "firebase": "^9.6.6", "firebaseui": "^6.0.0", "html-react-parser": "^3.0.7", "html2canvas": "^1.4.1", "jspdf": "^2.5.1", "lodash": "^4.17.21", "markdown-it": "^12.3.2", "proj4": "^2.19.10", "prop-types": "^15.8.1", "query-string": "^7.1.1", "react": "^17.0.2", "react-beautiful-dnd": "^13.1.0", "react-data-export": "^0.6.0", "react-datepicker": "^4.7.0", "react-dom": "^17.0.2", "react-dropzone": "^12.0.5", "react-firebaseui": "^6.0.0", "react-intl": "^5.24.6", "react-number-format": "^4.9.1", "react-router": "5.2.1", "react-router-dom": "^5.3.0", "react-scripts": "3.4.1", "react-share": "^5.0.3", "url": "^0.11.0", "xlsx": "^0.18.5"}, "devDependencies": {"@babel/core": "^7.14.8", "@babel/eslint-parser": "^7.14.7", "@babel/preset-env": "^7.14.8", "@babel/preset-react": "^7.14.5", "@typescript-eslint/parser": "^4.28.4", "babel-plugin-named-exports-order": "0.0.2", "babel-plugin-transform-imports": "^2.0.0", "cross-env": "^7.0.3", "eslint": "^6.6.0", "eslint-config-airbnb": "^18.2.1", "eslint-config-prettier": "^8.3.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-jsx-a11y": "^6.4.1", "eslint-plugin-prettier": "^3.4.0", "eslint-plugin-react": "^7.21.5", "eslint-plugin-react-hooks": "^1.7.0", "postcss-normalize": "^10.0.0", "prettier": "^2.3.2", "react-hot-loader": "^4.13.1", "sass": "^1.62.1", "typescript": "^4.3.5", "webpack": "^4.42.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}