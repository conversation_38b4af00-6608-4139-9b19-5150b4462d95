import React, { useContext, useEffect, useState } from "react";

// material ui
import Box from "@mui/material/Box";
import Grid from "@mui/material/Grid";
import Divider from "@mui/material/Divider";

// components
import LandTypeSelect from "./LandTypeSelect";
import DataFormSelect from "./DataFormSelect";
import DownLoadBtn from "./DownLoadBtn";
import InputColumn from "./InputColumn";

// utils
import { StoreContext } from "../../../../../store/StoreProvider";
import { Api, readOntoData } from "../../../../../api/land/Api";
import { isEmpty } from "../../../../../utils";
import Act from "../../../../../store/actions";

function LandData() {
  const [state, dispatch] = useContext(StoreContext);
  const { landType } = state.download;
  const [showInput, setShowInput] = useState(false);

  useEffect(() => {
    const apiStr = Api.getLandEvtTypes();
    readOntoData(apiStr).then((res) => {
      const tmpLandType = res.data.map((el) => ({ ...el, select: false }));
      dispatch({
        type: Act.SET_LANDTYPE,
        payload: tmpLandType,
      });
    });
  }, []);

  useEffect(() => {
    if (isEmpty(landType)) return;
    const check = landType.some((el) => el.select);
    setShowInput(check);
  }, [landType]);

  return (
    <Box className="LandData">
      <Grid container p={2} className="topArea">
        <Grid item xs={4} className="LandTypeSelect">
          <LandTypeSelect />
        </Grid>
        {showInput && (
          <Grid item mt={2}>
            <Divider />
            <DataFormSelect />
          </Grid>
        )}
      </Grid>
      {showInput && (
        <Grid container p={2} flexDirection="column">
          <Grid item style={{ width: "100%" }}>
            <Divider />
          </Grid>
          <Grid container item flexDirection="column">
            <Grid item>
              <InputColumn />
            </Grid>
          </Grid>
          <Grid item className="DownLoadBtn">
            <DownLoadBtn />
          </Grid>
        </Grid>
      )}
    </Box>
  );
}

export default LandData;
