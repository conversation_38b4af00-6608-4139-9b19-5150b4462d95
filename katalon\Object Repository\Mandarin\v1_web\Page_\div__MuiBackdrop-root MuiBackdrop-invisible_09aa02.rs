<?xml version="1.0" encoding="UTF-8"?>
<WebElementEntity>
   <description></description>
   <name>div__MuiBackdrop-root Mu<PERSON><PERSON>ackdrop-invisible_09aa02</name>
   <tag></tag>
   <elementGuidId>63dd2bbe-b36b-42dc-bdbe-e4450d67b08b</elementGuidId>
   <imagePath></imagePath>
   <selectorCollection>
      <entry>
         <key>XPATH</key>
         <value>//div[@id='menu-']/div</value>
      </entry>
      <entry>
         <key>IMAGE</key>
         <value></value>
      </entry>
      <entry>
         <key>CSS</key>
         <value>// 1. 點擊開啟下拉&#xd;
WebUI.click(findTestObject('Object Repository/dropdown_button'))&#xd;
&#xd;
// 2. 等待選單出現&#xd;
WebUI.waitForElementVisible(findTestObject('Object Repository/dropdown_menu_container'), 10)&#xd;
&#xd;
// 3. 用 JS 找選項文字並點擊&#xd;
String targetText = &quot;選項2&quot;  // 你想選的文字&#xd;
&#xd;
String script = &quot;&quot;&quot;&#xd;
  const options = document.querySelectorAll('li.MuiMenuItem-root');&#xd;
  for (let option of options) {&#xd;
    if(option.textContent.trim() === '${targetText}') {&#xd;
      option.click();&#xd;
      return true;&#xd;
    }&#xd;
  }&#xd;
  return false;&#xd;
&quot;&quot;&quot;&#xd;
&#xd;
Boolean clicked = WebUI.executeJavaScript(script, null)&#xd;
if (!clicked) {&#xd;
  KeywordUtil.markFailed(&quot;找不到選項文字：${targetText}&quot;)&#xd;
}&#xd;
</value>
      </entry>
      <entry>
         <key>BASIC</key>
         <value>id(&quot;menu-&quot;)/div[@class=&quot;MuiBackdrop-root MuiBackdrop-invisible MuiModal-backdrop css-g3hgs1-MuiBackdrop-root-MuiModal-backdrop&quot;]</value>
      </entry>
   </selectorCollection>
   <selectorMethod>CSS</selectorMethod>
   <smartLocatorCollection>
      <entry>
         <key>SMART_LOCATOR</key>
         <value>#menu- div >> nth=0</value>
      </entry>
   </smartLocatorCollection>
   <smartLocatorEnabled>false</smartLocatorEnabled>
   <useRalativeImagePath>true</useRalativeImagePath>
   <webElementProperties>
      <isSelected>false</isSelected>
      <matchCondition>equals</matchCondition>
      <name>tag</name>
      <type>Main</type>
      <value>div</value>
      <webElementGuid>866c3326-7276-4d96-8224-f1a9f8dc856c</webElementGuid>
   </webElementProperties>
   <webElementProperties>
      <isSelected>false</isSelected>
      <matchCondition>equals</matchCondition>
      <name>aria-hidden</name>
      <type>Main</type>
      <value>true</value>
      <webElementGuid>d87ea3c0-0ae4-409d-9e0b-1b0311dbecb7</webElementGuid>
   </webElementProperties>
   <webElementProperties>
      <isSelected>false</isSelected>
      <matchCondition>equals</matchCondition>
      <name>class</name>
      <type>Main</type>
      <value>MuiBackdrop-root MuiBackdrop-invisible MuiModal-backdrop css-g3hgs1-MuiBackdrop-root-MuiModal-backdrop</value>
      <webElementGuid>9a57fdb2-a497-47c8-95bf-c008fac03d87</webElementGuid>
   </webElementProperties>
   <webElementProperties>
      <isSelected>true</isSelected>
      <matchCondition>equals</matchCondition>
      <name>xpath</name>
      <type>Main</type>
      <value>id(&quot;menu-&quot;)/div[@class=&quot;MuiBackdrop-root MuiBackdrop-invisible MuiModal-backdrop css-g3hgs1-MuiBackdrop-root-MuiModal-backdrop&quot;]</value>
      <webElementGuid>d915448a-7d44-46aa-923a-0a30c2ccaa34</webElementGuid>
   </webElementProperties>
   <webElementXpaths>
      <isSelected>true</isSelected>
      <matchCondition>equals</matchCondition>
      <name>xpath:idRelative</name>
      <type>Main</type>
      <value>//div[@id='menu-']/div</value>
      <webElementGuid>c8e58bda-2eda-4c56-8137-f72c69c11f86</webElementGuid>
   </webElementXpaths>
   <webElementXpaths>
      <isSelected>false</isSelected>
      <matchCondition>equals</matchCondition>
      <name>xpath:position</name>
      <type>Main</type>
      <value>//div[2]/div</value>
      <webElementGuid>a684dc21-421d-446c-9179-9047c47b54db</webElementGuid>
   </webElementXpaths>
</WebElementEntity>
