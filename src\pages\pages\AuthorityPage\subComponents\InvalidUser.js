import React, {useContext} from 'react';
import {
    Checkbox,
    Paper,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow
} from "@mui/material";
import {StoreContext} from "../../../../store/StoreProvider";
import Act from "../../../../store/actions";
import {updateUser} from "../../../../Component/Authenticate/firebase/realtimeDatabase";

function InvalidUser() {
    const header = {userName: "使用者名稱", email: "信箱", checkBox: "確認"};

    const [state, dispatch] = useContext(StoreContext);
    const { userInfo, roles } = state.authority;

    const handleClick = (user) => {
        const tmpUserInfo = JSON.parse(JSON.stringify(userInfo));
        const findUser = tmpUserInfo.find(element => element.uid === user.uid);
        if (findUser) {
            const tmpRole = findUser.role.split(",");
            tmpRole.push("reader");
            findUser.role = tmpRole.join();
            dispatch({
                type: Act.SET_USERINFO,
                payload: tmpUserInfo
            });

            // update firebase
            updateUser(findUser.uid, findUser);
        }
    }

    return (
        <TableContainer component={Paper} className="ValidUser">
            <Table>
                <TableHead className="tableHead">
                    <TableRow>
                        {Object.keys(header).map((col,index) => (
                            <TableCell key={index} align="center">{header[col]}</TableCell>
                        ))}
                    </TableRow>
                </TableHead>
                <TableBody>
                    {userInfo
                        .filter(info => {
                            let exist = true;
                            roles.forEach(role => {
                                if (info.role.indexOf(role.toLowerCase()) >= 0 || info.role.indexOf("developer") >= 0) {
                                    exist = false;
                                }
                            })
                            return exist;
                        })
                        .map((user, index) => {
                        return (
                            <TableRow key={index}>
                                <TableCell align="center">{user.displayName}</TableCell>
                                <TableCell align="center">{user.email}</TableCell>
                                <TableCell align="center">
                                    <Checkbox onClick={() => handleClick(user)}/>
                                </TableCell>
                            </TableRow>
                        );
                    })}
                </TableBody>
            </Table>
        </TableContainer>
    );
}

export default InvalidUser;