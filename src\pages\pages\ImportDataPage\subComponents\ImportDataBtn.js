import React, { useContext } from "react";
import PropTypes from "prop-types";
import axios from "axios";

// material ui
import Button from "@mui/material/Button";

// store
import { StoreContext } from "../../../../store/StoreProvider";
import { Api, dataSet } from "../../../../api/land/Api";

// utils
import { isEmpty } from "../../../../utils";

function ImportDataBtn({ setCheckMsg }) {
  const [state] = useContext(StoreContext);
  const { fileData } = state.importData;
  const { displayName, email } = state.user;

  const startImport = () => {
    // api import
    const apiStr = Api.importToDB.replace("{graph}", dataSet);

    const formData = new FormData();

    // ※formData 要放的是 file 物件不是字串
    formData.append("file", fileData);
    formData.append("userName", displayName);
    formData.append("userEmail", email);

    // start send to api
    axios.post(apiStr, formData, {
      headers: { "Content-Type": "multipart/form-data" },
    });
  };

  const handleClick = () => {
    const msg =
      "開始匯入。您可以關閉此視窗，資料匯入完成後將寄發通知信至您的信箱。";
    setCheckMsg(msg);
    startImport();
  };

  return (
    <Button
      variant="contained"
      size="medium"
      onClick={handleClick}
      color="error"
      disabled={isEmpty(fileData)}
    >
      匯入資料
    </Button>
  );
}

ImportDataBtn.propTypes = {
  /** 設定顯示開始匯入訊息callback */
  setCheckMsg: PropTypes.func,
};

ImportDataBtn.defaultProps = {
  /** 設定顯示開始匯入訊息callback */
  setCheckMsg: () => "",
};

export default ImportDataBtn;
