import { checkRes } from "../../config";

const checkHeaderCell = (headerArr, fbHeader) => {
  const tmpHeaderArr = headerArr
    .filter((el) => el)
    .map((value) => ({ id: value || "" }));

  if (tmpHeaderArr.some(({ id }) => !id)) return checkRes.failed;

  const check = tmpHeaderArr.every((obj) => {
    const findEl = fbHeader.find((el) => el.id === obj.id);
    return !!findEl;
  });

  return check ? checkRes.success : checkRes.failed;
};
export default checkHeaderCell;
